# 网页资源抽取工具 - 项目说明文档

## 项目概述

本项目是一个专门用于抓取四川公共资源交易网站数据的自动化工具，支持批量获取招投标、签约履行等公共资源交易信息，并提供图形化界面和数据导出功能。

## 核心功能

### 1. 数据抓取
- **目标网站**: 四川公共资源交易网 (https://ggzyjy.sc.gov.cn)
- **数据类型**: 签约履行信息、工程建设项目、中标结果公示
- **抓取方式**: API调用 + HTML解析 + 浏览器自动化

### 2. 数据提取字段
- 项目基本信息：项目名称、项目所在地
- 发包人信息：发包人名称、地址、电话
- 承包人信息：承包人名称、地址、电话、项目经理
- 合同信息：签约合同价、签订日期、计划工期
- 时间节点：开标时间、公示期、计划开工/竣工日期

### 3. 多地区支持
- **四川省级**: 使用统一的省级API接口
- **宜宾市级**: 专门的宜宾市API接口适配

## 技术架构

### 前端技术
- **GUI框架**: PyQt5
- **界面组件**: 时间选择器、进度条、日志显示、数据预览

### 后端技术
- **网络请求**: requests库
- **HTML解析**: BeautifulSoup4
- **浏览器自动化**: Playwright
- **并发处理**: ThreadPoolExecutor + threading
- **数据导出**: openpyxl (Excel格式)

### 数据处理
- **多源数据融合**: 511.json、512.json、513.json
- **表格解析**: 智能识别HTML表格结构
- **数据清洗**: 字段标准化和过滤

## 项目结构

```
网页资源抽取/
├── crawler.py              # 基础爬虫脚本
├── client_app.py           # 四川省数据抽取GUI客户端
├── yibin_client_app.py     # 宜宾市专用客户端
├── requirements.txt        # 依赖包列表
├── README.md              # 基础说明文档
├── build/                 # 构建输出目录
├── dist/                  # 打包后的可执行文件
├── *.spec                 # PyInstaller配置文件
└── *.log                  # 运行日志文件
```

## 安装和使用

### 环境要求
- Python 3.7+
- Windows 10/11 (推荐)

### 依赖安装
```bash
# 使用国内镜像安装依赖
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt

# 安装浏览器驱动（仅crawler.py需要）
playwright install chromium
```

### 运行方式

#### 1. 基础爬虫
```bash
python crawler.py
```

#### 2. 四川省GUI客户端
```bash
python client_app.py
```

#### 3. 宜宾市GUI客户端
```bash
python yibin_client_app.py
```

#### 4. 可执行文件
直接运行 `dist/` 目录下的 `.exe` 文件

## 功能特性

### 1. 智能数据抓取
- 自动处理网页加载和JavaScript渲染
- 智能重试机制和错误恢复
- 支持大批量数据并发处理

### 2. 多格式数据解析
- HTML表格自动识别和解析
- JSON数据结构化提取
- 文本内容智能清洗

### 3. 用户友好界面
- 直观的时间范围选择
- 实时进度显示和日志输出
- 数据预览和导出功能

### 4. 高性能处理
- 多线程并发抓取
- 内存优化的数据处理
- 可配置的并发数量

## 数据处理流程

1. **初始化** → 设置时间范围和查询参数
2. **API请求** → 获取项目列表和基础信息
3. **详情抓取** → 并发访问每个项目详情页
4. **多源融合** → 整合不同JSON接口的数据
5. **表格解析** → 从HTML中提取结构化信息
6. **数据清洗** → 标准化字段格式
7. **结果导出** → 生成Excel报表

## 配置说明

### 并发设置
- 默认最大线程数: 10
- 可根据网络环境调整

### 时间范围
- 支持自定义日期区间
- 预设快捷选项（近1天、近7天、近30天）

### 导出格式
- Excel (.xlsx) - 默认格式
- 包含数据验证和格式化

## 注意事项

### 1. 网络要求
- 需要稳定的互联网连接
- 建议在网络环境良好时使用

### 2. 使用限制
- 请遵守目标网站的使用条款
- 避免过于频繁的请求
- 建议在非高峰时段使用

### 3. 数据准确性
- 数据来源于公开的政府网站
- 建议定期验证数据准确性
- 如发现异常请及时反馈

## 故障排除

### 常见问题
1. **网络连接失败**: 检查网络连接和防火墙设置
2. **数据解析错误**: 可能是网站结构变更，需要更新解析逻辑
3. **程序崩溃**: 查看日志文件定位具体错误

### 日志文件
- `client_app.log` - GUI客户端日志
- `crawler.log` - 基础爬虫日志

## 更新日志

### 版本特性
- 支持多地区数据源
- 优化并发处理性能
- 增强数据解析准确性
- 改进用户界面体验

## 技术支持

如遇到技术问题或需要功能扩展，请：
1. 查看日志文件确定错误原因
2. 检查网络连接和目标网站状态
3. 验证依赖包版本兼容性

## 免责声明

本工具仅用于学习和研究目的，使用者应遵守相关法律法规和网站使用条款。开发者不承担因使用本工具而产生的任何法律责任。
