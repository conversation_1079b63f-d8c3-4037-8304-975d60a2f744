from playwright.sync_api import sync_playwright
import json
import time
import logging
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("crawler.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def run():
    with sync_playwright() as p:
        # 启动浏览器
        browser = p.chromium.launch(headless=False)
        context = browser.new_context(viewport={"width": 1280, "height": 800})
        
        # 创建页面并监听请求和响应
        page = context.new_page()
        
        # 监听所有网络响应
        def handle_response(response):
            if "jyxx" in response.url and response.status == 200:
                try:
                    content_type = response.headers.get('content-type', '')
                    if 'application/json' in content_type or 'text/json' in content_type:
                        response_body = response.json()
                        logger.info(f"请求URL: {response.url}")
                        logger.info(f"请求参数: {response.request.post_data if response.request.post_data else '无参数'}")
                        logger.info(f"响应状态: {response.status}")
                        logger.info(f"响应内容: {json.dumps(response_body, ensure_ascii=False)}")
                        
                        # 提取并整理linkurl
                        if isinstance(response_body, dict) and "data" in response_body:
                            data = response_body["data"]
                            if isinstance(data, list):
                                for item in data:
                                    if "linkurl" in item:
                                        logger.info(f"提取的linkurl: {item['linkurl']}")
                except Exception as e:
                    logger.error(f"处理响应时出错: {str(e)}")
        
        page.on("response", handle_response)
        
        try:
            # 打开指定页面
            logger.info("打开页面")
            page.goto("https://ggzyjy.sc.gov.cn/jyxx/transactionInfo.html", timeout=60000, wait_until="networkidle")
            
            # 等待页面完全加载
            page.wait_for_load_state("domcontentloaded")
            page.wait_for_load_state("networkidle")
            time.sleep(5)  # 额外等待以确保JS完全执行
            
            # 保存初始页面的截图
            page.screenshot(path="initial_page.png")
            
            # 手动点击"工程建设"按钮
            logger.info("开始点击工程建设按钮")
            
            # 尝试直接用JavaScript点击
            try:
                # 查找所有可能的"工程建设"链接
                js_result = page.evaluate("""
                () => {
                    // 通过标题查找
                    const byTitle = Array.from(document.querySelectorAll('a[title="工程建设"]'));
                    
                    // 通过文本内容查找
                    const byText = Array.from(document.querySelectorAll('a'))
                        .filter(a => a.textContent.includes('工程建设'));
                    
                    // 通过href查找
                    const byHref = Array.from(document.querySelectorAll('a[href*="002001"]'));
                    
                    const allMatches = [...byTitle, ...byText, ...byHref];
                    
                    // 如果找到了元素，点击第一个
                    if (allMatches.length > 0) {
                        console.log('找到工程建设按钮，正在点击');
                        allMatches[0].click();
                        return true;
                    }
                    
                    return false;
                }
                """)
                
                if js_result:
                    logger.info("已通过JavaScript点击工程建设按钮")
                else:
                    logger.warning("未通过JavaScript找到工程建设按钮")
                    
                    # 如果JavaScript方法失败，尝试通过URL直接访问
                    logger.info("尝试直接访问工程建设页面")
                    page.goto("https://ggzyjy.sc.gov.cn/jyxx/002001/transactionInfo.html", timeout=60000)
            except Exception as e:
                logger.error(f"JavaScript点击失败: {str(e)}")
                # 尝试通过URL直接访问
                logger.info("尝试直接访问工程建设页面")
                page.goto("https://ggzyjy.sc.gov.cn/jyxx/002001/transactionInfo.html", timeout=60000)
            
            # 等待页面加载
            page.wait_for_load_state("domcontentloaded")
            page.wait_for_load_state("networkidle")
            time.sleep(5)  # 增加等待时间
            
            # 保存工程建设页面的截图
            page.screenshot(path="after_gongcheng_page.png")
            logger.info("已保存工程建设页面截图")
            
            # 针对签约履行按钮，使用更直接的方法定位和点击
            logger.info("开始查找并点击签约履行按钮")
            
            # 先分析页面上所有的菜单项目
            menu_items = page.evaluate("""
            () => {
                return Array.from(document.querySelectorAll('a, li, div, span, button'))
                    .filter(el => el.innerText && el.innerText.trim().length > 0)
                    .map(el => ({
                        text: el.innerText.trim(),
                        tag: el.tagName,
                        id: el.id || '',
                        class: el.className || '',
                        href: el.href || '',
                        visible: el.offsetParent !== null,
                        rect: el.getBoundingClientRect ? {
                            top: el.getBoundingClientRect().top,
                            left: el.getBoundingClientRect().left,
                            width: el.getBoundingClientRect().width,
                            height: el.getBoundingClientRect().height
                        } : {}
                    }))
                    .filter(item => item.visible);
            }
            """)
            
            # 记录所有可见的菜单项
            logger.info(f"页面上有 {len(menu_items)} 个可见元素")
            
            # 寻找可能与"签约履行"相关的菜单项
            found_qianyue_element = False
            for idx, item in enumerate(menu_items):
                if "签约" in item["text"] or "履行" in item["text"] or "合同" in item["text"]:
                    logger.info(f"找到可能的签约履行相关元素[{idx}]: {item}")
                    found_qianyue_element = True
            
            if not found_qianyue_element:
                logger.warning("在可见元素中没有找到签约履行相关文本")
                
                # 如果在可见元素中找不到，尝试查找所有元素（包括不可见的）
                all_elements = page.evaluate("""
                () => {
                    return Array.from(document.querySelectorAll('a, li, div, span, button'))
                        .filter(el => el.innerText && el.innerText.trim().length > 0)
                        .map(el => ({
                            text: el.innerText.trim(),
                            tag: el.tagName,
                            visible: el.offsetParent !== null
                        }))
                        .filter(item => item.text.includes('签约') || item.text.includes('履行') || item.text.includes('合同'));
                }
                """)
                
                logger.info(f"找到 {len(all_elements)} 个包含签约履行相关文本的元素（包括不可见的）")
                if len(all_elements) > 0:
                    logger.info(f"这些元素是: {all_elements}")
            
            # 尝试使用多种选择器定位签约履行按钮并点击
            selectors_to_try = [
                "text='签约履行'",
                "a:has-text('签约履行')",
                "li:has-text('签约履行')",
                "text='合同履行'",
                "text='签约'",
                "text='履行'",
                "a[title*='签约']",
                "a[title*='履行']"
            ]
            
            clicked = False
            for selector in selectors_to_try:
                try:
                    logger.info(f"尝试使用选择器 {selector} 查找签约履行按钮")
                    elements = page.query_selector_all(selector)
                    if len(elements) > 0:
                        logger.info(f"使用选择器 {selector} 找到了 {len(elements)} 个元素")
                        # 点击第一个找到的元素
                        elements[0].click(timeout=10000)
                        logger.info(f"成功点击了选择器 {selector} 找到的元素")
                        clicked = True
                        # 等待页面响应
                        page.wait_for_load_state("networkidle")
                        time.sleep(3)
                        break
                except Exception as e:
                    logger.warning(f"使用选择器 {selector} 点击失败: {str(e)}")
            
            if not clicked:
                # 如果所有选择器都失败了，尝试使用JavaScript查找和点击
                logger.info("尝试使用JavaScript定位并点击签约履行按钮")
                js_clicked = page.evaluate("""
                () => {
                    // 所有可能的关键词组合
                    const keywords = ['签约履行', '合同履行', '签约', '履行', '合同管理'];
                    
                    for (const keyword of keywords) {
                        // 通过文本内容精确匹配
                        const exactMatches = Array.from(document.querySelectorAll('a, li, div, span, button'))
                            .filter(el => el.innerText && el.innerText.trim() === keyword);
                        
                        if (exactMatches.length > 0) {
                            console.log(`找到精确匹配"${keyword}"的元素`);
                            exactMatches[0].click();
                            return {clicked: true, method: 'exact', keyword};
                        }
                        
                        // 通过包含文本匹配
                        const containsMatches = Array.from(document.querySelectorAll('a, li, div, span, button'))
                            .filter(el => el.innerText && el.innerText.includes(keyword));
                        
                        if (containsMatches.length > 0) {
                            console.log(`找到包含"${keyword}"的元素`);
                            containsMatches[0].click();
                            return {clicked: true, method: 'contains', keyword};
                        }
                    }
                    
                    // 如果上面都失败，尝试查找左侧菜单中的项目
                    const menuItems = Array.from(document.querySelectorAll('.leftmenu a, .left-menu a, .sidebar a, nav a, .menu a'));
                    const menuTexts = menuItems.map(el => el.innerText.trim());
                    console.log('找到的菜单项:', menuTexts);
                    
                    // 尝试点击第二个或第三个菜单项（假设签约履行可能在这个位置）
                    if (menuItems.length > 2) {
                        menuItems[2].click();
                        return {clicked: true, method: 'menu-position', position: 2};
                    } else if (menuItems.length > 1) {
                        menuItems[1].click();
                        return {clicked: true, method: 'menu-position', position: 1};
                    }
                    
                    return {clicked: false};
                }
                """)
                
                if js_clicked.get('clicked'):
                    logger.info(f"通过JavaScript成功点击了元素，方法: {js_clicked.get('method')}")
                    clicked = True
                else:
                    logger.warning("所有方法都无法定位和点击签约履行按钮")
            
            # 保存尝试点击签约履行后的页面截图
            page.screenshot(path="after_click_qianyue.png")
            logger.info("已保存尝试点击签约履行后的页面截图")
            
            # 等待页面加载和数据请求完成
            page.wait_for_load_state("networkidle")
            time.sleep(15)  # 给足够时间让页面加载和处理数据
            
            logger.info("任务完成")
        except Exception as e:
            logger.error(f"爬虫运行出错: {str(e)}", exc_info=True)
        finally:
            time.sleep(5)  # 保持浏览器打开一段时间，以便查看
            browser.close()

if __name__ == "__main__":
    run() 