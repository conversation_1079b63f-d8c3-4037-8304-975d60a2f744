import sys
import logging
import threading
import queue
import concurrent.futures
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QTextEdit, QSplitter, QLabel, QFileDialog, QHBoxLayout, QComboBox, QFormLayout, QDateEdit, QGridLayout, QFrame, QProgressBar
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QDate
from PyQt5.QtGui import QPixmap, QImage
import os
import time
import json
import io
import requests
from datetime import datetime, timedelta, date
from bs4 import BeautifulSoup
import re
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side
from openpyxl.utils import get_column_letter
import csv

# 定义需要过滤掉的列 (只保留日期相关列，不过滤承包人相关信息)
FILTERED_COLUMNS = [
    "承包人项目负责人（勘察、设计、监理、货物适用）",
    "证件及证号",
    "签约合同日期（勘察、设计、监理、货物适用）",
    "签订合同日期（施工适用）",
    "签订合同日期（监理、勘察、设计、货物适用）",
    "签订合同日期（施工、监理适用）",
    "签订合同日期（勘察、设计、货物适用）",
    "计划交工日期", 
    "计划完成日期", 
    "计划开始日期", 
    "计划开工日期", 
    "计划竣工日期"
]

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("client_app.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LogHandler(logging.Handler):
    def __init__(self, signal):
        super().__init__()
        self.signal = signal

    def emit(self, record):
        log_entry = self.format(record)
        self.signal.emit(log_entry)

class PageWorker(threading.Thread):
    def __init__(self, page, page_size, category, start_date_str, end_date_str, result_queue, log_queue):
        super().__init__()
        self.page = page
        self.page_size = page_size
        self.category = category
        self.start_date_str = start_date_str
        self.end_date_str = end_date_str
        self.result_queue = result_queue
        self.log_queue = log_queue

    def run(self):
        try:
            # 记录日志
            self.log_queue.put(f"线程开始请求第 {self.page + 1} 页数据...")

            # API地址
            api_url = "https://ggzyjy.sc.gov.cn/inteligentsearch/rest/esinteligentsearch/getFullTextDataNew"

            # 设置请求头
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            # 请求参数 - 根据页码设置正确的pn值
            # pn值应该是page_size * page (页码从0开始)
            pn_value = self.page_size * self.page
            
            # 请求参数
            payload = {
                "token": "",
                "pn": pn_value,  # 实际页码位置，根据page_size计算
                "rn": self.page_size,  # 每页记录数
                "sdt": "",
                "edt": "",
                "wd": "",
                "inc_wd": "",
                "exc_wd": "",
                "fields": "",
                "cnum": "",
                "sort": "{\"ordernum\":\"0\",\"webdate\":\"0\"}",
                "ssort": "",
                "cl": 10000,
                "terminal": "",
                "condition": [
                    {
                        "fieldName": "categorynum",
                        "equal": self.category,
                        "notEqual": None,
                        "equalList": None,
                        "notEqualList": None,
                        "isLike": True,
                        "likeType": 2
                    }
                ],
                "time": [
                    {
                        "fieldName": "webdate",
                        "startTime": self.start_date_str,
                        "endTime": self.end_date_str
                    }
                ],
                "highlights": "",
                "statistics": None,
                "unionCondition": None,
                "accuracy": "",
                "noParticiple": "1",
                "searchRange": None,
                "noWd": True
            }
            
            self.log_queue.put(f"设置pn值为: {pn_value}，当前页码: {self.page + 1}")

            self.log_queue.put(f"线程发送请求到: {api_url}")

            # 发送POST请求
            response = requests.post(api_url, headers=headers, json=payload)

            # 检查响应状态
            self.log_queue.put(f"线程响应状态码: {response.status_code}")

            if response.status_code == 200:
                # 解析响应内容
                response_data = response.json()

                # 根据API响应结构提取数据
                if 'result' in response_data and 'records' in response_data['result']:
                    # 提取记录列表
                    records = response_data['result']['records']
                    self.log_queue.put(f"线程获取到 {len(records)} 条记录")

                    # 处理每条记录，提取linkurl
                    link_urls = []
                    for record in records:
                        link_url = record.get('linkurl', '')
                        title = record.get('title', '无标题')
                        if link_url:
                            # 直接使用原始linkurl值
                            link_urls.append(link_url)
                            self.log_queue.put(f"线程提取linkurl: {link_url} - {title}")

                    # 将结果放入队列
                    self.result_queue.put((self.page, link_urls))

                    # 返回总记录数和总页数（仅第一页需要）
                    if self.page == 0:
                        total_records = response_data['result'].get('totalcount', 0)
                        total_pages = (total_records + self.page_size - 1) // self.page_size if total_records > 0 else 0
                        self.result_queue.put(('total', (total_records, total_pages)))
                else:
                    self.log_queue.put("线程API响应数据结构不符合预期，未找到records字段")
                    self.result_queue.put((self.page, []))
            else:
                self.log_queue.put(f"线程请求失败，状态码: {response.status_code}")
                self.result_queue.put((self.page, []))

        except Exception as e:
            self.log_queue.put(f"线程错误: {str(e)}")
            self.result_queue.put((self.page, []))

class JsonFetcherThread(threading.Thread):
    """用于并行获取JSON数据的线程"""

    def __init__(self, relateinfoid, headers, log_queue, result_queue):
        super().__init__()
        self.relateinfoid = relateinfoid
        self.headers = headers
        self.log_queue = log_queue
        self.result_queue = result_queue

    def run(self):
        try:
            # 生成当前时间戳
            timestamp = int(time.time() * 1000)
            json_url = f"https://ggzyjy.sc.gov.cn/staticJson/{self.relateinfoid}/512.json?_={timestamp}"

            self.log_queue.put(f"线程请求JSON数据: {json_url}")

            # 发送GET请求
            json_response = requests.get(json_url, headers=self.headers, timeout=30)

            if json_response.status_code == 200:
                json_data = json_response.json()
                self.log_queue.put(f"成功获取JSON数据: {self.relateinfoid}")

                # 提取infoContent字段
                info_content = None
                if json_data and isinstance(json_data, dict) and 'infoContent' in json_data:
                    info_content = json_data['infoContent']
                    self.log_queue.put("成功提取infoContent字段")

                # 解析发包人信息
                sender_info = self.parse_sender_info(json_data)
                if sender_info:
                    self.log_queue.put(f"成功解析发包人信息: {sender_info}")

                # 返回结果
                result = {
                    'full_json': json_data,
                    'info_content': info_content,
                    'sender_info': sender_info
                }

                self.result_queue.put((self.relateinfoid, result, json_url))
            else:
                self.log_queue.put(f"JSON请求失败，状态码: {json_response.status_code}")
                self.result_queue.put((self.relateinfoid, {"error": f"JSON请求失败，状态码: {json_response.status_code}"}, json_url))

        except Exception as e:
            self.log_queue.put(f"JSON请求线程错误: {str(e)}")
            self.result_queue.put((self.relateinfoid, {"error": f"JSON请求错误: {str(e)}"}, None))

    def parse_sender_info(self, json_data):
        """解析发包人名称、地址和电话信息，以及项目名称等信息"""
        # 使用空字典而不是预设字段，让字段动态获取
        sender_info = {}

        try:
            self.log_queue.put(f"开始解析信息...")

            # 定义关键字段列表
            key_fields = ["项目名称", "项目所在地", "发包人名称", "发包人地址", "发包人电话",
                        "承包人名称", "承包人地址", "承包人电话", "签约合同价（元）",
                        "签约合同价（其他价格形式）", "签订合同日期（施工、监理适用）",
                        "计划开工日期", "计划交工日期", "计划竣工日期",
                        "签订合同日期（勘察、设计、货物适用）", "计划开始日期", "计划完成日期"]

            # 只从infoContent字段提取信息
            if json_data and isinstance(json_data, dict) and 'infoContent' in json_data:
                info_content = json_data['infoContent']
                self.log_queue.put(f"找到infoContent字段，长度: {len(str(info_content)) if info_content else 0}")

                # 如果infoContent是HTML内容，直接解析HTML
                if isinstance(info_content, str) and ('<html' in info_content.lower() or '<table' in info_content.lower()):
                    self.log_queue.put("检测到infoContent包含HTML内容，尝试解析...")
                    try:
                        soup = BeautifulSoup(info_content, 'html.parser')

                        # 查找表格
                        tables = soup.find_all('table')
                        self.log_queue.put(f"在HTML中找到 {len(tables)} 个表格")

                        # 遍历所有表格
                        for table_idx, table in enumerate(tables):
                            self.log_queue.put(f"开始解析表格 {table_idx+1}")

                            # 遍历表格行
                            rows = table.find_all('tr')
                            for row_idx, row in enumerate(rows):
                                cells = row.find_all(['td', 'th'])
                                if not cells:
                                    continue

                                row_data = [cell.get_text().strip() for cell in cells]

                                # 检查这一行是否包含日期字段
                                date_field_row = False
                                for text in row_data:
                                    if text in ["计划开工日期", "计划交工日期", "计划竣工日期", "计划开始日期", "计划完成日期"]:
                                        date_field_row = True
                                        break

                                if date_field_row:
                                    # 找到日期字段行，保存字段名
                                    headers = row_data

                                    # 如果有下一行，这应该是对应的数据行
                                    if row_idx + 1 < len(rows):
                                        data_cells = rows[row_idx + 1].find_all(['td', 'th'])
                                        data_values = [cell.get_text().strip() for cell in data_cells]

                                        # 确保数据行长度和标题行相同
                                        if len(data_values) == len(headers):
                                            for i, header in enumerate(headers):
                                                if header in key_fields:  # 只保存关键字段
                                                    sender_info[header] = data_values[i]
                                                    self.log_queue.put(f"从表格中提取到日期信息: {header} = {data_values[i]}")

                                # 检查常规键值对
                                elif len(row_data) >= 2:
                                    key = row_data[0].strip()
                                    value = row_data[1].strip()

                                    # 只有当key是关键字段且value不是关键字段时才添加
                                    if key in key_fields and value not in key_fields:
                                        sender_info[key] = value
                                        self.log_queue.put(f"从表格中提取到信息: {key} = {value}")

                    except Exception as e:
                        self.log_queue.put(f"解析HTML内容时出错: {str(e)}")

            # 检查是否有找到任何信息
            has_info = bool(sender_info)
            if has_info:
                self.log_queue.put(f"成功提取到信息: {sender_info}")
                return sender_info
            else:
                self.log_queue.put("未能提取到任何信息")
                # 返回空字典
                return {}

        except Exception as e:
            self.log_queue.put(f"解析信息时出错: {str(e)}")
            # 返回空字典
            return {}

    def extract_table_content(self, table):
        """简化的表格内容提取函数，采用两遍扫描策略来识别标题行和数据行"""
        try:
            # 获取所有行，不区分thead和tbody
            all_rows = table.find_all('tr')
            row_count = len(all_rows)
            self.log_queue.put(f"表格包含 {row_count} 行")

            if row_count == 0:
                self.log_queue.put("表格没有行，跳过")
                return []

            # 第一遍扫描：收集所有可能的列名
            column_names = set()
            for row in all_rows:
                cells = row.find_all(['th', 'td'])
                for cell in cells:
                    text = cell.get_text().strip()
                    # 以下字段是常见的列名
                    if text in ["项目名称", "项目所在地", "发包人名称", "发包人地址", "发包人电话",
                              "承包人名称", "承包人地址", "承包人电话", "签约合同价（元）",
                              "签约合同价（其他价格形式）", "签订合同日期（施工、监理适用）",
                              "计划开工日期", "计划交工日期", "计划竣工日期",
                              "签订合同日期（勘察、设计、货物适用）", "计划开始日期", "计划完成日期",
                              "承包人承担的工作", "质量要求", "承包人项目经理（施工适用）",
                              "证件及证号", "技术负责人（项目总工）", "承包人项目负责人（勘察、设计、监理、货物适用）"]:
                        column_names.add(text)

            self.log_queue.put(f"识别到的列名: {column_names}")

            # 第二遍扫描：分析每行是标题行还是数据行
            processed_rows = []
            data_rows = []
            current_headers = []

            for i, row in enumerate(all_rows):
                cells = row.find_all(['th', 'td'])
                row_data = [cell.get_text().strip() for cell in cells]

                # 检查这一行是否包含已知的列名
                is_header_row = False
                for cell_text in row_data:
                    if cell_text in column_names:
                        is_header_row = True
                        break

                if is_header_row:
                    # 如果当前行是标题行，且我们已经收集了数据，先保存之前的数据
                    if current_headers and data_rows:
                        for data_row in data_rows:
                            if len(data_row) == len(current_headers):
                                processed_row = {}
                                for j, header in enumerate(current_headers):
                                    processed_row[header] = data_row[j]
                                processed_rows.append(processed_row)

                    # 开始新的标题-数据组合
                    current_headers = row_data
                    data_rows = []
                else:
                    # 数据行，添加到当前标题对应的数据中
                    data_rows.append(row_data)

            # 处理最后一组标题-数据
            if current_headers and data_rows:
                for data_row in data_rows:
                    if len(data_row) == len(current_headers):
                        processed_row = {}
                        for j, header in enumerate(current_headers):
                            processed_row[header] = data_row[j]
                        processed_rows.append(processed_row)

            # 最终结果应该是一个二维数组，而不是字典列表
            # 把processed_rows转换为二维数组格式
            result = []
            if processed_rows:
                # 获取所有可能的列名
                all_keys = set()
                for row in processed_rows:
                    all_keys.update(row.keys())

                # 创建表头行
                headers = list(all_keys)
                result.append(headers)

                # 创建数据行
                for row in processed_rows:
                    data_row = []
                    for key in headers:
                        data_row.append(row.get(key, ""))
                    result.append(data_row)
            else:
                # 如果没有处理出任何行，返回一个空的二维数组
                # 至少有一个空的表头行
                result.append([])

            log_queue.put(f"表格解析完成，共提取 {len(result) - 1} 行数据")
            return result
        except Exception as e:
            log_queue.put(f"提取表格内容时出错: {str(e)}")
            # 确保始终返回一个有效的列表
            return []

class DataFetcherThread(QThread):
    log_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int, int)  # 当前项，总项数
    finished_signal = pyqtSignal(list)  # 传递抓取到的数据列表

    def __init__(self, urls, max_workers=10):
        super().__init__()
        self.urls = urls
        self.max_workers = max_workers  # 最大线程数

    def run(self):
        try:
            all_data = []
            total_urls = len(self.urls)

            self.log_signal.emit(f"开始并行访问 {total_urls} 个URL并提取数据...")

            # 设置请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            # 创建结果队列和日志队列
            result_queue = queue.Queue()
            log_queue = queue.Queue()

            # 使用线程池并行处理所有URL
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有任务
                futures = []
                for i, url in enumerate(self.urls):
                    # 确保URL有前缀
                    if not url.startswith('http'):
                        full_url = f"https://ggzyjy.sc.gov.cn{url}"
                    else:
                        full_url = url

                    # 创建并提交任务
                    future = executor.submit(self.process_url, full_url, headers, i, log_queue, result_queue)
                    futures.append(future)

                # 等待任务完成并更新进度
                completed = 0
                for future in concurrent.futures.as_completed(futures):
                    completed += 1
                    self.progress_signal.emit(completed, total_urls)

                    # 处理日志
                    while not log_queue.empty():
                        log_message = log_queue.get()
                        self.log_signal.emit(log_message)

            # 收集所有结果
            while not result_queue.empty():
                page_data = result_queue.get()
                if page_data:  # 确保数据有效
                    all_data.append(page_data)

            # 确保按原始URL顺序排序结果
            all_data.sort(key=lambda x: self.urls.index(x.get('original_url', '')))

            self.log_signal.emit(f"所有URL处理完成，成功提取 {len(all_data)} 条数据")
            self.finished_signal.emit(all_data)

        except Exception as e:
            self.log_signal.emit(f"数据获取线程出错: {str(e)}")
            self.finished_signal.emit([])

    def process_url(self, full_url, headers, index, log_queue, result_queue):
        try:
            log_queue.put(f"正在访问 ({index+1}/{len(self.urls)}): {full_url}")

            response = requests.get(full_url, headers=headers, timeout=30)

            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                title = soup.title.string if soup.title else "无标题"
                relateinfoid = None
                relateinfoid_span = soup.find('span', id='relateinfoid')
                if relateinfoid_span:
                    relateinfoid = relateinfoid_span.get('data-value')
                    log_queue.put(f"成功提取relateinfoid: {relateinfoid}")

                json_data = None
                info_content = None
                sender_info = None
                try:
                    if relateinfoid:
                        timestamp_512 = int(time.time() * 1000)
                        json_url_512 = f"https://ggzyjy.sc.gov.cn/staticJson/{relateinfoid}/512.json?_={timestamp_512}"
                        resp_512 = requests.get(json_url_512, headers=headers, timeout=30)
                        if resp_512.status_code == 200:
                            json_data = resp_512.json()
                            log_queue.put(f"成功获取512.json数据")
                            if json_data and isinstance(json_data, dict) and 'infoContent' in json_data:
                                info_content = json_data['infoContent']
                                log_queue.put("成功提取infoContent字段")
                            sender_info = self.parse_sender_info(json_data, log_queue)
                        else:
                            log_queue.put(f"512.json请求失败，状态码: {resp_512.status_code}")
                except Exception as e:
                    log_queue.put(f"请求/512.json或解析签约履行时出错: {str(e)}")

                # 新增：请求511.json，提取公示期
                gongshi_period = ''
                try:
                    if relateinfoid:
                        timestamp_511 = int(time.time() * 1000)
                        json_url_511 = f"https://ggzyjy.sc.gov.cn/staticJson/{relateinfoid}/511.json?_={timestamp_511}"
                        resp_511 = requests.get(json_url_511, headers=headers, timeout=30)
                        if resp_511.status_code == 200:
                            data_511 = resp_511.json()
                            if isinstance(data_511, dict) and 'data' in data_511 and isinstance(data_511['data'], list) and data_511['data']:
                                info_511 = data_511['data'][0]
                                info_content_511 = info_511.get('infoContent', '')
                                if info_content_511 and ("<table" in info_content_511 or "<html" in info_content_511):
                                    soup_info = BeautifulSoup(info_content_511, 'html.parser')
                                    tables_info = soup_info.find_all('table')
                                    found = False
                                    for table in tables_info:
                                        for row in table.find_all('tr'):
                                            cells = row.find_all(['td', 'th'])
                                            if len(cells) >= 2:
                                                key = cells[0].get_text().strip()
                                                if "公示期" in key:
                                                    gongshi_period = cells[1].get_text().strip()
                                                    log_queue.put(f"[511]提取到公示期: {gongshi_period}")
                                                    found = True
                                                    break
                                        if found:
                                            break
                        else:
                            log_queue.put(f"511.json请求失败，状态码: {resp_511.status_code}")
                except Exception as e:
                    log_queue.put(f"请求/511.json或解析公示期时出错: {str(e)}")

                kaibiao_time = ""
                kaibiao_extra = {}
                project_name = None
                try:
                    if relateinfoid:
                        timestamp_513 = int(time.time() * 1000)
                        json_url_513 = f"https://ggzyjy.sc.gov.cn/staticJson/{relateinfoid}/513.json?_={timestamp_513}"
                        resp_513 = requests.get(json_url_513, headers=headers, timeout=30)
                        if resp_513.status_code == 200:
                            data_513 = resp_513.json()
                            info_content_513 = ""
                            if isinstance(data_513, dict):
                                if 'infoContent' in data_513 and isinstance(data_513['infoContent'], str):
                                    info_content_513 = data_513['infoContent']
                                elif 'data' in data_513 and isinstance(data_513['data'], list) and data_513['data']:
                                    info_content_513 = data_513['data'][0].get('infoContent', '')
                                    d = data_513['data'][0]
                                    # --- 项目名称提取逻辑重写 ---
                                    project_name = None
                                    if 'thepublicinfo' in d:
                                        log_queue.put(f"[DEBUG] d['thepublicinfo']: {d['thepublicinfo']}")
                                        if isinstance(d['thepublicinfo'], dict):
                                            pn = d['thepublicinfo'].get('PROJECT_NAME', None)
                                            log_queue.put(f"[DEBUG] d['thepublicinfo']['PROJECT_NAME']: {pn}")
                                            if pn is not None:
                                                project_name = pn
                                                log_queue.put(f"[513]thepublicinfo项目名称: {project_name}")
                                    # 2. 513.json title
                                    if not project_name or not str(project_name).strip():
                                        pn = d.get('title', None)
                                        if pn and str(pn).strip():
                                            project_name = pn
                                            log_queue.put(f"[513]title项目名称: {project_name}")
                                    # 3. 表格
                                    if not project_name or not str(project_name).strip():
                                        for table in table_data:
                                            if not isinstance(table, list) or len(table) < 2:
                                                continue
                                            headers = table[0] if isinstance(table[0], list) else []
                                            if not headers:
                                                continue
                                            try:
                                                idx = headers.index("项目名称")
                                            except ValueError:
                                                continue
                                            for row in table[1:]:
                                                if idx < len(row) and row[idx] and str(row[idx]).strip():
                                                    project_name = row[idx]
                                                    log_queue.put(f"[表格]项目名称: {project_name}")
                                                    break
                                                if project_name:
                                                    break
                                    # 4. URL fallback
                                    if not project_name or not str(project_name).strip():
                                        import os
                                        url_filename = os.path.basename(full_url).replace(".html", "")
                                        project_name = f"项目-{url_filename}"
                                        log_queue.put(f"[URL]项目名称: {project_name}")

                                    # 解析更多字段
                                    mapping = [
                                        ("zhuanzai", "转载来源"),
                                        ("title", "标题"),
                                        ("TENDER_PROJECT_CODE", "招标项目代码"),
                                        ("infoDate", "信息发布日期"),
                                        ("thepublicinfo", None),
                                    ]
                                    for k, cn in mapping:
                                        if k == "thepublicinfo" and isinstance(d.get(k), dict):
                                            pub = d[k]
                                            pub_map = [
                                                ("LEGAL_PERSON", "法定代表人"),
                                                ("REGION_CODE", "行政区划代码"),
                                                ("PROJECT_CODE", "项目代码"),
                                                ("APPROVAL_NAME", "批复名称"),
                                                ("PROJECT_NAME", "项目名称"),
                                                ("CONTACTOR", "联系人"),
                                                ("ADDRESS", "地址"),
                                                ("CREATE_TIME", "创建时间"),
                                                ("CONTACT_INFORMATION", "联系方式"),
                                            ]
                                            for pk, pcn in pub_map:
                                                v = pub.get(pk, "")
                                                if v:
                                                    kaibiao_extra[pcn] = v
                                                    log_queue.put(f"[513]提取到 {pcn}: {v}")
                                        else:
                                            v = d.get(k, "")
                                            if v and cn:
                                                kaibiao_extra[cn] = v
                                                log_queue.put(f"[513]提取到 {cn}: {v}")
                            if info_content_513 and ("<table" in info_content_513 or "<html" in info_content_513):
                                soup_513 = BeautifulSoup(info_content_513, 'html.parser')
                                tables_513 = soup_513.find_all('table')
                                found_kaibiao = False
                                for table in tables_513:
                                    for row in table.find_all('tr'):
                                        cells = row.find_all(['td', 'th'])
                                        for i, cell in enumerate(cells):
                                            key = cell.get_text().strip().replace('：', '').replace(':', '')
                                            if '开标时间' in key and i+1 < len(cells):
                                                value = cells[i+1].get_text().strip()
                                                if value:
                                                    kaibiao_time = value
                                                    log_queue.put(f"[513]提取到开标时间: {kaibiao_time}")
                                                    found_kaibiao = True
                                                    break
                                        if found_kaibiao:
                                            break
                                    if found_kaibiao:
                                        break
                        else:
                            log_queue.put(f"513.json请求失败，状态码: {resp_513.status_code}")
                except Exception as e:
                    log_queue.put(f"请求/513.json或解析中标结果公示时出错: {str(e)}")

                # 其余内容照常处理
                content_div = soup.find('div', class_='detail_content')
                if content_div and content_div.text.strip():
                    content = content_div.text.strip()
                else:
                    content = f"使用JSON地址获取数据: {json_url_512 if relateinfoid else ''}" if relateinfoid else "无法提取正文内容"

                tables = soup.find_all('table')
                log_queue.put(f"在页面中找到 {len(tables)} 个表格")
                table_data = []
                for table in tables:
                    try:
                        table_content = self.extract_table_content(table, log_queue)
                        if table_content and isinstance(table_content, list):
                            table_data.append(table_content)
                    except Exception as e:
                        log_queue.put(f"处理表格内容时出错: {str(e)}")

                # 补充：如上面都没提取到项目名称，尝试从表格、页面title、URL提取
                if not project_name:
                    # 表格
                    for table in table_data:
                        if not isinstance(table, list) or len(table) < 2:
                            continue
                        headers = table[0] if isinstance(table[0], list) else []
                        if not headers:
                            continue
                        try:
                            idx = headers.index("项目名称")
                        except ValueError:
                            continue
                        for row in table[1:]:
                            if idx < len(row) and row[idx]:
                                project_name = row[idx]
                                log_queue.put(f"[表格]项目名称: {project_name}")
                                break
                        if project_name:
                            break
                if not project_name:
                    # 页面title
                    if title and title != "四川省公共资源交易信息网":
                        project_name = title.replace("四川省公共资源交易信息网", "").strip()
                        if project_name:
                            log_queue.put(f"[页面title]项目名称: {project_name}")
                if not project_name:
                    # URL fallback
                    import os
                    url_filename = os.path.basename(full_url).replace(".html", "")
                    project_name = f"项目-{url_filename}"
                    log_queue.put(f"[URL]项目名称: {project_name}")

                page_data = {
                    'url': full_url,
                    'original_url': full_url,
                    'title': title,
                    'content': content,
                    'tables': table_data if table_data else [],
                    'relateinfoid': relateinfoid,
                    'json_url': json_url_512 if relateinfoid else '',
                    'json_data': json_data if json_data else {},
                    'info_content': info_content if info_content else '',
                    'sender_info': sender_info if sender_info else {},
                    'contract_data': {},
                    'contractor_info': {},
                    'html': str(soup),
                    '公示期': gongshi_period,
                    '开标时间': kaibiao_time,
                    '开标时间额外信息': kaibiao_extra,
                    '项目名称': project_name
                }
                result_queue.put(page_data)
                log_queue.put(f"成功提取数据: {project_name}")
            else:
                log_queue.put(f"请求失败，状态码: {response.status_code}")
                page_data = {
                    'url': full_url,
                    'original_url': full_url,
                    'title': '无标题',
                    'content': '',
                    'tables': [],
                    'relateinfoid': '',
                    'json_url': '',
                    'json_data': {},
                    'info_content': '',
                    'sender_info': {},
                    'contract_data': {},
                    'contractor_info': {},
                    'html': '',
                    '公示期': '',
                    '开标时间': '',
                    '开标时间额外信息': {},
                    '项目名称': ''
                }
                result_queue.put(page_data)
        except Exception as e:
            log_queue.put(f"处理URL时出错: {str(e)}")
            page_data = {
                'url': full_url,
                'original_url': full_url,
                'title': '无标题',
                'content': '',
                'tables': [],
                'relateinfoid': '',
                'json_url': '',
                'json_data': {},
                'info_content': '',
                'sender_info': {},
                'contract_data': {},
                'contractor_info': {},
                'html': '',
                '公示期': '',
                '开标时间': '',
                '开标时间额外信息': {},
                '项目名称': ''
            }
            result_queue.put(page_data)

    def extract_table_content(self, table, log_queue):
        """简化的表格内容提取函数，采用两遍扫描策略来识别标题行和数据行"""
        try:
            # 获取所有行，不区分thead和tbody
            all_rows = table.find_all('tr')
            row_count = len(all_rows)
            log_queue.put(f"表格包含 {row_count} 行")

            if row_count == 0:
                log_queue.put("表格没有行，跳过")
                return []

            # 第一遍扫描：收集所有可能的列名
            column_names = set()
            for row in all_rows:
                cells = row.find_all(['th', 'td'])
                for cell in cells:
                    text = cell.get_text().strip()
                    # 以下字段是常见的列名
                    if text in ["项目名称", "项目所在地", "发包人名称", "发包人地址", "发包人电话",
                              "承包人名称", "承包人地址", "承包人电话", "签约合同价（元）",
                              "签约合同价（其他价格形式）", "签订合同日期（施工、监理适用）",
                              "计划开工日期", "计划交工日期", "计划竣工日期",
                              "签订合同日期（勘察、设计、货物适用）", "计划开始日期", "计划完成日期",
                              "承包人承担的工作", "质量要求", "承包人项目经理（施工适用）",
                              "证件及证号", "技术负责人（项目总工）", "承包人项目负责人（勘察、设计、监理、货物适用）"]:
                        column_names.add(text)

            log_queue.put(f"识别到的列名: {column_names}")

            # 第二遍扫描：分析每行是标题行还是数据行
            processed_rows = []
            data_rows = []
            current_headers = []

            for i, row in enumerate(all_rows):
                cells = row.find_all(['th', 'td'])
                row_data = [cell.get_text().strip() for cell in cells]

                # 检查这一行是否包含已知的列名
                is_header_row = False
                for cell_text in row_data:
                    if cell_text in column_names:
                        is_header_row = True
                        break

                if is_header_row:
                    # 如果当前行是标题行，且我们已经收集了数据，先保存之前的数据
                    if current_headers and data_rows:
                        for data_row in data_rows:
                            if len(data_row) == len(current_headers):
                                processed_row = {}
                                for j, header in enumerate(current_headers):
                                    processed_row[header] = data_row[j]
                                processed_rows.append(processed_row)

                    # 开始新的标题-数据组合
                    current_headers = row_data
                    data_rows = []
                else:
                    # 数据行，添加到当前标题对应的数据中
                    data_rows.append(row_data)

            # 处理最后一组标题-数据
            if current_headers and data_rows:
                for data_row in data_rows:
                    if len(data_row) == len(current_headers):
                        processed_row = {}
                        for j, header in enumerate(current_headers):
                            processed_row[header] = data_row[j]
                        processed_rows.append(processed_row)

            # 最终结果应该是一个二维数组，而不是字典列表
            # 把processed_rows转换为二维数组格式
            result = []
            if processed_rows:
                # 获取所有可能的列名
                all_keys = set()
                for row in processed_rows:
                    all_keys.update(row.keys())

                # 创建表头行
                headers = list(all_keys)
                result.append(headers)

                # 创建数据行
                for row in processed_rows:
                    data_row = []
                    for key in headers:
                        data_row.append(row.get(key, ""))
                    result.append(data_row)
            else:
                # 如果没有处理出任何行，返回一个空的二维数组
                # 至少有一个空的表头行
                result.append([])

            log_queue.put(f"表格解析完成，共提取 {len(result) - 1} 行数据")
            return result
        except Exception as e:
            log_queue.put(f"提取表格内容时出错: {str(e)}")
            # 确保始终返回一个有效的列表
            return []

    def parse_sender_info(self, json_data, log_queue):
        """解析发包人名称、地址和电话信息，以及项目名称等信息"""
        # 使用空字典而不是预设字段，让字段动态获取
        sender_info = {}

        try:
            log_queue.put(f"开始解析信息...")

            # 定义关键字段列表
            key_fields = ["项目名称", "项目所在地", "发包人名称", "发包人地址", "发包人电话",
                        "承包人名称", "承包人地址", "承包人电话", "签约合同价（元）",
                        "签约合同价（其他价格形式）", "签订合同日期（施工、监理适用）",
                        "计划开工日期", "计划交工日期", "计划竣工日期",
                        "签订合同日期（勘察、设计、货物适用）", "计划开始日期", "计划完成日期"]

            # 1. 先从整个HTML中查找表格数据
            if isinstance(json_data, dict) and 'html' in json_data:
                html_content = json_data['html']
                log_queue.put("尝试从完整HTML中提取信息")
                try:
                    soup = BeautifulSoup(html_content, 'html.parser')
                    tables = soup.find_all('table')
                    log_queue.put(f"在完整HTML中找到 {len(tables)} 个表格")

                    # 处理表格
                    for table in tables:
                        self.extract_info_from_table(table, sender_info, key_fields, log_queue)
                except Exception as e:
                    log_queue.put(f"从HTML提取信息出错: {str(e)}")

            # 2. 从infoContent字段提取信息
            if not sender_info and json_data and isinstance(json_data, dict) and 'infoContent' in json_data:
                info_content = json_data['infoContent']
                log_queue.put(f"从infoContent字段提取信息，长度: {len(str(info_content)) if info_content else 0}")

                # 如果infoContent是HTML内容，解析HTML
                if isinstance(info_content, str) and ('<html' in info_content.lower() or '<table' in info_content.lower()):
                    log_queue.put("检测到infoContent包含HTML内容，尝试解析...")
                    try:
                        soup = BeautifulSoup(info_content, 'html.parser')
                        tables = soup.find_all('table')
                        log_queue.put(f"在infoContent中找到 {len(tables)} 个表格")

                        # 处理表格
                        for table in tables:
                            self.extract_info_from_table(table, sender_info, key_fields, log_queue)
                    except Exception as e:
                        log_queue.put(f"解析infoContent HTML内容时出错: {str(e)}")

            # 3. 如果之前的方法未提取到信息，尝试从JSON数据中查找
            if isinstance(json_data, dict):
                for key, value in json_data.items():
                    if key in key_fields and isinstance(value, str):
                        # 只在该字段尚未存在时添加
                        if key not in sender_info:
                            sender_info[key] = value
                            log_queue.put(f"从JSON直接提取: {key} = {value}")

            # 检查是否有找到任何信息
            has_info = bool(sender_info)
            if has_info:
                log_queue.put(f"成功提取到信息: {sender_info}")
                return sender_info
            else:
                log_queue.put("未能提取到任何信息")
                # 返回空字典
                return {}

        except Exception as e:
            log_queue.put(f"解析信息时出错: {str(e)}")
            # 返回空字典
            return {}

    def extract_info_from_table(self, table, result_dict, key_fields, log_queue):
        """从表格中提取关键信息"""
        try:
            rows = table.find_all('tr')
            log_queue.put(f"表格包含 {len(rows)} 行")
            
            # 使用多种方法提取信息，但避免重复
            
            # 方法1：检查常规键值对行
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:  # 至少有两列，一列为键，一列为值
                    key = cells[0].get_text().strip()
                    value = cells[1].get_text().strip()
                    
                    # 只保存关键字段且不覆盖已有值
                    if key in key_fields and value and value not in key_fields and key not in result_dict:
                        result_dict[key] = value
                        log_queue.put(f"从表格提取键值对: {key} = {value}")
                        
                        # 特殊处理承包人和发包人信息（通常这些字段在连续列中）
                        if key == "承包人名称" and len(cells) > 2:
                            # 尝试获取承包人地址和电话
                            if len(cells) > 2 and "承包人地址" not in result_dict:
                                addr = cells[2].get_text().strip()
                                if addr and addr not in key_fields:
                                    result_dict["承包人地址"] = addr
                                    log_queue.put(f"关联提取: 承包人地址 = {addr}")
                            if len(cells) > 3 and "承包人电话" not in result_dict:
                                phone = cells[3].get_text().strip()
                                if phone and phone not in key_fields:
                                    result_dict["承包人电话"] = phone
                                    log_queue.put(f"关联提取: 承包人电话 = {phone}")
                        
                        elif key == "发包人名称" and len(cells) > 2:
                            # 尝试获取发包人地址和电话
                            if len(cells) > 2 and "发包人地址" not in result_dict:
                                addr = cells[2].get_text().strip()
                                if addr and addr not in key_fields:
                                    result_dict["发包人地址"] = addr
                                    log_queue.put(f"关联提取: 发包人地址 = {addr}")
                            if len(cells) > 3 and "发包人电话" not in result_dict:
                                phone = cells[3].get_text().strip()
                                if phone and phone not in key_fields:
                                    result_dict["发包人电话"] = phone
                                    log_queue.put(f"关联提取: 发包人电话 = {phone}")
            
            # 方法2：检查承包人字段所在列
            # 只在关键字段缺失时使用此方法
            if "承包人名称" not in result_dict or "承包人地址" not in result_dict or "承包人电话" not in result_dict:
                header_row = None
                headers = []
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    cell_texts = [cell.get_text().strip() for cell in cells]
                    # 检查是否包含承包人名称列
                    if "承包人名称" in cell_texts:
                        header_row = row
                        headers = cell_texts
                        break
                
                # 如果找到包含承包人名称的表头行
                if header_row and "承包人名称" in headers:
                    contractor_name_idx = headers.index("承包人名称")
                    # 尝试查找承包人地址和电话列
                    addr_idx = -1
                    phone_idx = -1
                    for i, header in enumerate(headers):
                        if "承包人地址" in header:
                            addr_idx = i
                        elif "承包人电话" in header:
                            phone_idx = i
                    
                    # 如果找到相关列，尝试从下一行提取数据
                    next_row_idx = rows.index(header_row) + 1
                    if next_row_idx < len(rows):
                        data_row = rows[next_row_idx]
                        data_cells = data_row.find_all(['td', 'th'])
                        data_values = [cell.get_text().strip() for cell in data_cells]
                        
                        # 提取承包人名称（只在未提取到时）
                        if "承包人名称" not in result_dict and contractor_name_idx < len(data_values) and data_values[contractor_name_idx]:
                            result_dict["承包人名称"] = data_values[contractor_name_idx]
                            log_queue.put(f"从表格列提取: 承包人名称 = {data_values[contractor_name_idx]}")
                        
                        # 提取承包人地址（只在未提取到时）
                        if "承包人地址" not in result_dict and addr_idx >= 0 and addr_idx < len(data_values) and data_values[addr_idx]:
                            result_dict["承包人地址"] = data_values[addr_idx]
                            log_queue.put(f"从表格列提取: 承包人地址 = {data_values[addr_idx]}")
                        
                        # 提取承包人电话（只在未提取到时）
                        if "承包人电话" not in result_dict and phone_idx >= 0 and phone_idx < len(data_values) and data_values[phone_idx]:
                            result_dict["承包人电话"] = data_values[phone_idx]
                            log_queue.put(f"从表格列提取: 承包人电话 = {data_values[phone_idx]}")
            
            # 方法3：检查标题行和数据行的组合
            # 仅在关键字段缺失时进行
            for row_idx, row in enumerate(rows):
                cells = row.find_all(['td', 'th'])
                if not cells:
                    continue
                
                # 提取当前行所有单元格文本
                row_data = [cell.get_text().strip() for cell in cells]
                
                # 检查这是否是包含关键字段的标题行
                found_missing_field = False
                for field in key_fields:
                    if field not in result_dict and field in row_data:
                        found_missing_field = True
                        break
                
                if found_missing_field and row_idx + 1 < len(rows):
                    # 找到标题行，尝试配对下一行作为数据行
                    header_cells = row_data
                    data_row = rows[row_idx + 1]
                    data_cells = [cell.get_text().strip() for cell in data_row.find_all(['td', 'th'])]
                    
                    # 确保数据行长度和标题行相匹配
                    if len(data_cells) >= len(header_cells):
                        for i, header in enumerate(header_cells):
                            if header in key_fields and header not in result_dict and i < len(data_cells):
                                value = data_cells[i]
                                if value and value not in key_fields:
                                    result_dict[header] = value
                                    log_queue.put(f"从标题-数据行对提取: {header} = {value}")
        except Exception as e:
            log_queue.put(f"从表格提取信息时出错: {str(e)}")
            
    def parse_contract(self, html_content, log_queue):
        """解析合同详细信息，考虑标题行和数据行的正确区分"""
        try:
            log_queue.put("开始解析合同详细信息...")

            # 初始化一个空的合同数据结构
            contract_data = {}

            # 确保有HTML内容
            if not html_content:
                log_queue.put("无法解析合同信息：没有HTML内容")
                return contract_data

            # 解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')

            # 查找所有表格
            tables = soup.find_all('table')
            log_queue.put(f"找到 {len(tables)} 个表格")

            # 定义关键字段列表
            key_fields = ["项目名称", "项目所在地", "发包人名称", "发包人地址", "发包人电话",
                         "承包人名称", "承包人地址", "承包人电话", "签约合同价（元）",
                         "签约合同价（其他价格形式）", "签订合同日期（施工、监理适用）",
                         "计划开工日期", "计划交工日期", "计划竣工日期",
                         "签订合同日期（勘察、设计、货物适用）", "计划开始日期", "计划完成日期"]

            # 遍历所有表格，寻找包含日期信息的表格
            for table_idx, table in enumerate(tables):
                # 提取表格内容
                rows = table.find_all('tr')

                # 遍历每一行
                for row_idx, row in enumerate(rows):
                    cells = row.find_all('td')
                    if not cells:
                        continue

                    row_data = [cell.get_text().strip() for cell in cells]

                    # 检查是否是包含日期字段的行
                    date_field_row = False
                    for text in row_data:
                        if text in ["计划开工日期", "计划交工日期", "计划竣工日期", "计划开始日期", "计划完成日期"]:
                            date_field_row = True
                            break

                    if date_field_row:
                        # 找到日期字段行，保存字段名
                        headers = row_data

                        # 如果有下一行，这应该是对应的数据行
                        if row_idx + 1 < len(rows):
                            data_cells = rows[row_idx + 1].find_all('td')
                            data_values = [cell.get_text().strip() for cell in data_cells]

                            # 确保数据行长度和标题行相同
                            if len(data_values) == len(headers):
                                for i, header in enumerate(headers):
                                    if header in key_fields:  # 只保存关键字段
                                        contract_data[header] = data_values[i]
                                        log_queue.put(f"从表格中提取到日期信息: {header} = {data_values[i]}")

                    # 检查常规键值对
                    elif len(row_data) >= 2:
                        key = row_data[0].strip()
                        value = row_data[1].strip()

                        # 只有当key是关键字段且value不是列标题时才添加
                        if key in key_fields and value not in key_fields:
                            contract_data[key] = value
                            log_queue.put(f"从表格中提取到信息: {key} = {value}")

            log_queue.put(f"合同信息提取完成，成功提取 {len(contract_data)} 个字段")
            return contract_data

        except Exception as e:
            log_queue.put(f"解析合同信息时出错: {str(e)}")
            return {}

class ApiCrawlerThread(QThread):
    log_signal = pyqtSignal(str)
    data_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int, int)  # 当前页码，总页数
    finished_signal = pyqtSignal(list)  # 传递linkurl列表

    def __init__(self, category, days=None, custom_range=None):
        super().__init__()
        self.category = category
        self.days = days
        self.custom_range = custom_range  # (start_date, end_date) 元组
        self.max_threads = 5

    def run(self):
        # 创建一个日志处理器，将日志发送到UI
        handler = LogHandler(self.log_signal)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logging.getLogger().addHandler(handler)

        try:
            self.log_signal.emit("开始请求API数据...")

            # 使用系统当前年份
            current_time = datetime.now()

            # 根据是否有自定义日期范围决定查询时间
            if self.custom_range:
                # 使用自定义日期范围
                start_date = datetime.combine(self.custom_range[0], datetime.min.time())  # 当天00:00:00
                end_date = datetime.combine(self.custom_range[1], datetime.max.time())    # 当天23:59:59
                date_range_desc = f"自定义范围({self.custom_range[0].strftime('%Y-%m-%d')}至{self.custom_range[1].strftime('%Y-%m-%d')})"
            else:
                # 使用预设天数
                end_date = datetime(current_time.year, current_time.month, current_time.day, 23, 59, 59)
                start_date = datetime(current_time.year, current_time.month, current_time.day, 0, 0, 0) - timedelta(days=self.days-1)
                date_range_desc = f"{self.days}天"

            # 格式化日期为API需要的格式
            start_date_str = start_date.strftime("%Y-%m-%d %H:%M:%S")
            end_date_str = end_date.strftime("%Y-%m-%d %H:%M:%S")

            self.log_signal.emit(f"查询时间范围: {start_date_str} 至 {end_date_str} ({date_range_desc})")
            self.log_signal.emit(f"查询分类: 签约履行 ({self.category})")

            # 创建列表保存所有记录的linkurl
            all_link_urls = []

            # 初始页码
            page_size = 12  # 每页记录数

            # 首先获取第一页，确定总页数
            result_queue = queue.Queue()
            log_queue = queue.Queue()

            first_page_worker = PageWorker(
                0, page_size, self.category,
                start_date_str, end_date_str,
                result_queue, log_queue
            )
            first_page_worker.start()
            first_page_worker.join()

            # 处理日志
            while not log_queue.empty():
                self.log_signal.emit(log_queue.get())

            # 获取第一页结果和总页数
            total_records = 0
            total_pages = 0
            first_page_links = []

            while not result_queue.empty():
                item = result_queue.get()
                if item[0] == 'total':
                    total_records, total_pages = item[1]
                    self.log_signal.emit(f"总记录数: {total_records}, 总页数: {total_pages}")
                else:
                    first_page_links = item[1]
                    all_link_urls.extend(first_page_links)

            # 如果有多页，使用多线程获取剩余页面
            if total_pages > 1:
                self.log_signal.emit(f"使用多线程获取剩余 {total_pages - 1} 页数据...")

                # 创建结果字典，用于按页码顺序存储结果
                results = {0: first_page_links}

                # 创建线程池
                threads = []
                active_threads = 0

                # 创建队列存储结果和日志
                result_queue = queue.Queue()
                log_queue = queue.Queue()

                # 处理剩余页面
                for page in range(1, total_pages):
                    # 限制最大线程数
                    while active_threads >= self.max_threads:
                        time.sleep(0.1)
                        # 检查已完成的线程
                        threads = [t for t in threads if t.is_alive()]
                        active_threads = len(threads)

                    # 创建并启动新线程
                    worker = PageWorker(
                        page, page_size, self.category,
                        start_date_str, end_date_str,
                        result_queue, log_queue
                    )
                    worker.start()
                    threads.append(worker)
                    active_threads += 1

                    # 发送进度信息
                    self.progress_signal.emit(page + 1, total_pages)

                # 等待所有线程完成
                for t in threads:
                    t.join()

                # 处理日志
                while not log_queue.empty():
                    self.log_signal.emit(log_queue.get())

                # 处理结果
                while not result_queue.empty():
                    page, links = result_queue.get()
                    if isinstance(page, int):  # 排除'total'键
                        self.log_signal.emit(f"第 {page + 1} 页获取到 {len(links)} 个链接")
                        results[page] = links

                # 按页码顺序合并结果
                for page in range(1, total_pages):
                    if page in results:
                        all_link_urls.extend(results[page])
                        self.log_signal.emit(f"添加第 {page + 1} 页的 {len(results[page])} 个链接")

            # 发送处理后的数据用于导出
            self.log_signal.emit(f"共获取 {len(all_link_urls)} 个linkurl")
            self.finished_signal.emit(all_link_urls)

        except Exception as e:
            self.log_signal.emit(f"错误: {str(e)}")
            logger.error(f"爬虫运行出错: {str(e)}", exc_info=True)
            self.finished_signal.emit([])

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("四川公共资源交易网数据抽取工具")
        self.setGeometry(100, 100, 1280, 800)

        # 创建中央部件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # 创建顶部控制区域
        control_layout = QHBoxLayout()

        # 创建左侧表单布局
        form_layout = QFormLayout()

        # 添加时间范围选择下拉框
        self.days_combo = QComboBox()
        self.days_combo.addItem("近一天", 1)
        self.days_combo.addItem("近三天", 3)
        self.days_combo.addItem("近一周", 7)
        self.days_combo.addItem("近一月", 30)
        self.days_combo.addItem("近三月", 90)
        self.days_combo.addItem("自定义日期范围", -1)
        self.days_combo.currentIndexChanged.connect(self.on_date_option_changed)
        form_layout.addRow("发布时间:", self.days_combo)

        # 添加信息类型下拉框
        self.category_combo = QComboBox()
        self.category_combo.addItem("中标结果公示", "002001008")
        self.category_combo.addItem("签约履行", "002001007")
        self.category_combo.setCurrentIndex(0)
        form_layout.addRow("信息类型:", self.category_combo)

        control_layout.addLayout(form_layout)

        # 创建右侧日期选择区域
        self.date_selection_frame = QFrame()
        self.date_selection_frame.setFrameShape(QFrame.StyledPanel)
        self.date_selection_frame.setVisible(False)

        date_layout = QGridLayout(self.date_selection_frame)

        # 开始日期
        date_layout.addWidget(QLabel("开始日期:"), 0, 0)
        self.start_date = QDateEdit(self)
        self.start_date.setCalendarPopup(True)
        self.start_date.setDate(QDate.currentDate().addDays(-7))  # 默认7天前
        date_layout.addWidget(self.start_date, 0, 1)

        # 结束日期
        date_layout.addWidget(QLabel("结束日期:"), 0, 2)
        self.end_date = QDateEdit(self)
        self.end_date.setCalendarPopup(True)
        self.end_date.setDate(QDate.currentDate())  # 默认今天
        date_layout.addWidget(self.end_date, 0, 3)

        control_layout.addWidget(self.date_selection_frame)

        # 添加伸缩项，使控件靠左对齐
        control_layout.addStretch()

        main_layout.addLayout(control_layout)

        # 创建按钮布局
        button_layout = QHBoxLayout()

        # 创建开始爬取按钮
        self.start_button = QPushButton("开始获取数据")
        self.start_button.clicked.connect(self.start_crawler)
        button_layout.addWidget(self.start_button)

        # 创建访问并抓取数据按钮
        self.fetch_button = QPushButton("访问URL并导出数据")
        self.fetch_button.clicked.connect(self.fetch_and_export_data)
        self.fetch_button.setEnabled(False)  # 初始禁用
        button_layout.addWidget(self.fetch_button)

        # 创建刷新按钮
        self.refresh_button = QPushButton("刷新")
        self.refresh_button.clicked.connect(self.refresh_ui)
        button_layout.addWidget(self.refresh_button)

        main_layout.addLayout(button_layout)

        # 创建进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)

        # 创建水平分割器，左侧是日志，右侧是数据
        h_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(h_splitter)

        # 创建日志显示区域
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        h_splitter.addWidget(self.log_display)

        # 创建数据显示区域
        self.data_display = QTextEdit()
        self.data_display.setReadOnly(True)
        h_splitter.addWidget(self.data_display)

        # 创建状态标签
        self.status_label = QLabel("就绪")
        main_layout.addWidget(self.status_label)

        # 设置分割器的初始大小
        h_splitter.setSizes([400, 800])

        # 创建爬虫线程
        self.crawler_thread = None
        self.data_fetcher_thread = None

        # 保存提取的链接
        self.all_links = []

        # 保存提取的数据
        self.all_data = []

        # 取消自动启动
        self.auto_start = False

    def on_date_option_changed(self, index):
        # 当选择"自定义日期范围"时显示日期选择控件
        if self.days_combo.currentData() == -1:
            self.date_selection_frame.setVisible(True)
        else:
            self.date_selection_frame.setVisible(False)

    def start_crawler(self):
        if self.crawler_thread is None or not self.crawler_thread.isRunning():
            self.start_button.setEnabled(False)
            self.fetch_button.setEnabled(False)
            self.log_display.clear()
            self.data_display.clear()
            self.status_label.setText("正在获取数据...")
            self.log_display.append("准备请求数据...")

            # 获取选择的天数
            days = self.days_combo.currentData()
            # 获取信息类型
            category = self.category_combo.currentData()

            # 如果选择自定义日期范围
            if days == -1:
                # 直接从界面获取日期范围
                start_date = self.start_date.date().toPyDate()
                end_date = self.end_date.date().toPyDate()
                self.log_display.append(f"选择的时间范围: 自定义范围 ({start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')})")

                # 创建并启动爬虫线程
                self.crawler_thread = ApiCrawlerThread(category=category, custom_range=(start_date, end_date))
                self.crawler_thread.log_signal.connect(self.update_log)
                self.crawler_thread.data_signal.connect(self.update_data)
                self.crawler_thread.progress_signal.connect(self.update_progress)
                self.crawler_thread.finished_signal.connect(self.crawler_finished)
                self.crawler_thread.start()
            else:
                self.log_display.append(f"选择的时间范围: {self.days_combo.currentText()} ({days}天)")

                # 创建并启动爬虫线程
                self.crawler_thread = ApiCrawlerThread(category=category, days=days)
                self.crawler_thread.log_signal.connect(self.update_log)
                self.crawler_thread.data_signal.connect(self.update_data)
                self.crawler_thread.progress_signal.connect(self.update_progress)
                self.crawler_thread.finished_signal.connect(self.crawler_finished)
                self.crawler_thread.start()

    def update_log(self, message):
        self.log_display.append(message)
        # 滚动到底部
        self.log_display.verticalScrollBar().setValue(
            self.log_display.verticalScrollBar().maximum()
        )

    def update_data(self, data):
        self.data_display.append(data)
        # 滚动到底部
        self.data_display.verticalScrollBar().setValue(
            self.data_display.verticalScrollBar().maximum()
        )

    def update_progress(self, current, total):
        self.status_label.setText(f"正在获取数据... {current}/{total} 页")
        # 更新进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)

    def crawler_finished(self, links):
        self.start_button.setEnabled(True)
        self.all_links = links
        self.status_label.setText(f"数据获取完成，共获取 {len(links)} 个linkurl")
        self.update_log("数据获取完成")

        # 确保使用所有链接，不进行去重
        self.update_log(f"保留所有链接，共 {len(links)} 个")

        # 在数据显示区域显示所有链接
        self.data_display.clear()
        for i, link in enumerate(links, 1):
            # 确保URL有前缀
            if not link.startswith('http'):
                full_url = f"https://ggzyjy.sc.gov.cn{link}"
            else:
                full_url = link
            self.data_display.append(f"{i}. {full_url}")

        if links:  # 如果有数据，启用访问按钮
            self.fetch_button.setEnabled(True)

        # 隐藏进度条
        self.progress_bar.setVisible(False)

    def fetch_and_export_data(self):
        if not self.all_links:
            self.update_log("没有可访问的linkurl")
            return

        if self.data_fetcher_thread is not None and self.data_fetcher_thread.isRunning():
            self.update_log("正在处理数据，请等待...")
            return

        # 禁用按钮，避免重复操作
        self.start_button.setEnabled(False)
        self.fetch_button.setEnabled(False)

        # 清空数据显示区域
        self.data_display.clear()

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 准备带前缀的URL列表
        urls_with_prefix = []
        for link in self.all_links:
            if not link.startswith('http'):
                full_url = f"https://ggzyjy.sc.gov.cn{link}"
            else:
                full_url = link
            urls_with_prefix.append(full_url)

        self.update_log(f"准备访问 {len(urls_with_prefix)} 个URL")

        # 创建并启动数据获取线程
        self.data_fetcher_thread = DataFetcherThread(urls_with_prefix)
        self.data_fetcher_thread.log_signal.connect(self.update_log)
        self.data_fetcher_thread.progress_signal.connect(self.update_progress)
        self.data_fetcher_thread.finished_signal.connect(self.data_fetch_finished)
        self.data_fetcher_thread.start()

    def data_fetch_finished(self, data):
        self.all_data = data
        self.status_label.setText(f"数据访问完成，共获取 {len(data)} 条数据")
        self.update_log(f"数据访问完成，共获取 {len(data)} 条原始数据")

        # 重新启用按钮
        self.start_button.setEnabled(True)
        self.fetch_button.setEnabled(True)

        # 隐藏进度条
        self.progress_bar.setVisible(False)

        if not data:
            self.update_log("没有获取到任何数据")
            return

        # 过滤掉None值
        valid_data = [item for item in data if item is not None]
        if len(valid_data) < len(data):
            self.update_log(f"注意：过滤了 {len(data) - len(valid_data)} 条None数据")

        # 过滤掉非字典值
        dict_data = [item for item in valid_data if isinstance(item, dict)]
        if len(dict_data) < len(valid_data):
            self.update_log(f"注意：过滤了 {len(valid_data) - len(dict_data)} 条非字典数据")

        # 新增：如果选择的是"中标结果公示"，即使签约履行无数据，也保留所有中标结果公示数据
        category = self.category_combo.currentData() if hasattr(self, 'category_combo') else None
        if category == "002001008" and not dict_data:
            # 若主类型为中标结果公示且无有效数据，保留原始数据（不做任何过滤）
            dict_data = valid_data
            self.update_log("已保留所有中标结果公示原始数据")

        # 如果没有有效数据，则退出
        if not dict_data:
            self.update_log("没有有效的数据可以显示")
            return

        # 记录承包人信息的统计
        sender_info_count = sum(1 for item in dict_data if 'sender_info' in item and isinstance(item['sender_info'], dict) and item['sender_info'])
        self.update_log(f"含有承包人信息的数据：{sender_info_count}/{len(dict_data)}")

        # 显示提取到的数据摘要
        self.data_display.clear()
        for i, item in enumerate(dict_data, 1):
            try:
                title = item.get('title', '无标题')
                self.data_display.append(f"{i}. {title}")
                self.data_display.append(f"   URL: {item.get('url', '无URL')}")

                # 添加relateinfoid和JSON URL信息
                if 'relateinfoid' in item and item['relateinfoid']:
                    self.data_display.append(f"   RelateInfoID: {item['relateinfoid']}")

                if 'json_url' in item and item['json_url']:
                    self.data_display.append(f"   JSON URL: {item['json_url']}")

                # 添加发包人信息
                if 'sender_info' in item and isinstance(item['sender_info'], dict) and item['sender_info']:
                    sender_info = item['sender_info']
                    self.data_display.append("   【发包人信息】")
                    for key, value in sender_info.items():
                        if key in ["发包人名称", "发包人地址", "发包人电话", "承包人名称", "承包人地址", "承包人电话"]:
                            self.data_display.append(f"   {key}: {value}")

                # 安全获取内容并显示预览
                content = item.get('content', '')
                if not isinstance(content, str):
                    content = str(content)
                content_preview = content[:100] + "..." if len(content) > 100 else content
                self.data_display.append(f"   内容预览: {content_preview}")
                self.data_display.append("   " + "-" * 50)
            except Exception as e:
                self.update_log(f"显示数据项 {i} 时出错: {str(e)}")

        # 更新实际处理的数据
        self.all_data = dict_data
        self.update_log(f"最终有效数据条数：{len(dict_data)}")

        # 询问用户是否导出数据
        self.export_full_data()

    def export_full_data(self):
        if not self.all_data:
            self.update_log("没有可导出的数据")
            return

        # 确保所有数据都是有效的
        valid_data = [item for item in self.all_data if item is not None and isinstance(item, dict)]
        if not valid_data:
            self.update_log("没有有效的数据可以导出")
            return

        # 使用全部有效数据
        export_data = valid_data
        self.update_log(f"准备导出所有数据，共 {len(export_data)} 条")

        # 获取当前时间作为文件名的一部分
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 弹出文件保存对话框，默认选择Excel格式
        file_path, selected_filter = QFileDialog.getSaveFileName(
            self,
            "导出完整数据",
            f"四川公共资源交易完整数据_{timestamp}.xlsx",  # 默认文件名改为.xlsx后缀
            "Excel文件 (*.xlsx);;JSON文件 (*.json);;HTML文件 (*.html);;文本文件 (*.txt);;仅API数据 (*.json);;仅发包人信息 (*.json);;所有文件 (*.*)",  # Excel格式放在最前面
            "Excel文件 (*.xlsx)"  # 默认选中的过滤器
        )

        if not file_path:
            return

        try:
            file_ext = os.path.splitext(file_path)[1].lower()

            # 如果没有后缀名且选择了Excel格式，自动添加.xlsx后缀
            if not file_ext and "Excel" in selected_filter:
                file_path += ".xlsx"
                file_ext = ".xlsx"
                self.update_log("已自动添加.xlsx后缀")

            # 检查是否只导出API数据
            if file_path.endswith('_api.json'):
                # 只导出API数据
                api_data = []
                for item in export_data:  # 使用全部有效数据
                    if 'json_data' in item and item['json_data']:
                        api_item = {
                            'relateinfoid': item.get('relateinfoid', ''),
                            'title': item.get('title', ''),
                            'url': item.get('url', ''),
                            'api_data': item['json_data']
                        }
                        api_data.append(api_item)

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(api_data, f, ensure_ascii=False, indent=2)

                self.update_log(f"API数据已成功导出到: {file_path}")
                return

            # 检查是否只导出发包人信息
            if file_path.endswith('_sender.json'):
                # 只导出发包人信息
                sender_data = []
                for item in export_data:  # 使用全部有效数据
                    if 'sender_info' in item and item['sender_info']:
                        sender_item = {
                            'relateinfoid': item.get('relateinfoid', ''),
                            'title': item.get('title', ''),
                            'url': item.get('url', ''),
                            'sender_info': item['sender_info']
                        }
                        sender_data.append(sender_item)

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(sender_data, f, ensure_ascii=False, indent=2)

                self.update_log(f"发包人信息已成功导出到: {file_path}")
                return

            # 检查是否只导出infoContent内容
            if file_path.endswith('_info_content.json'):
                # 只导出infoContent内容
                content_data = []
                for item in export_data:  # 使用全部有效数据
                    if 'info_content' in item and item['info_content']:
                        content_item = {
                            'relateinfoid': item.get('relateinfoid', ''),
                            'title': item.get('title', ''),
                            'url': item.get('url', ''),
                            'info_content': item['info_content']
                        }
                        content_data.append(content_item)

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(content_data, f, ensure_ascii=False, indent=2)

                self.update_log(f"infoContent内容已成功导出到: {file_path}")
                return

            if file_ext == '.json':
                # 导出为JSON格式
                with open(file_path, 'w', encoding='utf-8') as f:
                    # 移除html字段以减小文件大小
                    export_data = []
                    for item in export_data:  # 使用全部有效数据
                        item_copy = item.copy()
                        if 'html' in item_copy:
                            del item_copy['html']
                        export_data.append(item_copy)

                    json.dump(export_data, f, ensure_ascii=False, indent=2)

            elif file_ext == '.html':
                # 导出为HTML格式
                with open(file_path, 'w', encoding='utf-8') as f:
                    html_content = """
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset="UTF-8">
                        <title>四川公共资源交易数据</title>
                        <style>
                            body { font-family: Arial, sans-serif; margin: 20px; }
                            .item { border: 1px solid #ddd; margin-bottom: 20px; padding: 15px; }
                            .title { font-size: 18px; font-weight: bold; }
                            .url { color: blue; }
                            .content { margin-top: 10px; }
                            .sender-info { background-color: #f0f8ff; padding: 10px; border: 1px solid #add8e6; margin: 10px 0; }
                            table { border-collapse: collapse; width: 100%; margin-top: 10px; }
                            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                            th { background-color: #f2f2f2; }
                            .json-data { background-color: #f8f8f8; padding: 10px; border: 1px solid #eee; overflow: auto; }
                            pre { white-space: pre-wrap; }
                        </style>
                    </head>
                    <body>
                        <h1>四川公共资源交易数据</h1>
                    """

                    for item in export_data:  # 使用全部有效数据
                        html_content += f"""
                        <div class="item">
                            <div class="title">{item['title']}</div>
                            <div class="url"><a href="{item['url']}" target="_blank">{item['url']}</a></div>
                        """

                        # 添加relateinfoid信息
                        if 'relateinfoid' in item and item['relateinfoid']:
                            html_content += f"<div><strong>RelateInfoID:</strong> {item['relateinfoid']}</div>"

                        # 添加发包人信息
                        if 'sender_info' in item and item['sender_info']:
                            sender_info = item['sender_info']
                            html_content += '<div class="sender-info">'
                            html_content += '<h3>发包人信息</h3>'
                            if sender_info.get('发包人名称'):
                                html_content += f"<p><strong>发包人名称:</strong> {sender_info['发包人名称']}</p>"
                            if sender_info.get('发包人地址'):
                                html_content += f"<p><strong>发包人地址:</strong> {sender_info['发包人地址']}</p>"
                            if sender_info.get('发包人电话'):
                                html_content += f"<p><strong>发包人电话:</strong> {sender_info['发包人电话']}</p>"
                            html_content += '</div>'

                        html_content += f"""
                            <div class="content">
                                <pre>{item['content']}</pre>
                            </div>
                        """

                        # 添加infoContent数据
                        if 'info_content' in item and item['info_content']:
                            html_content += f"""
                            <h3>InfoContent</h3>
                            <div class="json-data">
                                <pre>{item['info_content']}</pre>
                            </div>
                            """

                        # 添加JSON数据
                        if 'json_data' in item and item['json_data']:
                            html_content += f"""
                            <h3>API数据</h3>
                            <div class="json-data">
                                <pre>{json.dumps(item['json_data'], ensure_ascii=False, indent=2)}</pre>
                            </div>
                            """

                        # 添加表格数据
                        if item['tables']:
                            for table_idx, table in enumerate(item['tables']):
                                if table:  # 确保表格有数据
                                    html_content += f"<h3>表格 {table_idx + 1}</h3><table>"

                                    for row in table:
                                        html_content += "<tr>"
                                        for cell in row:
                                            html_content += f"<td>{cell}</td>"
                                        html_content += "</tr>"

                                    html_content += "</table>"

                        html_content += "</div>"

                    html_content += """
                    </body>
                    </html>
                    """

                    f.write(html_content)

            elif file_ext == '.txt':
                # 导出为文本格式
                with open(file_path, 'w', encoding='utf-8') as f:
                    for item in export_data:  # 使用全部有效数据
                        f.write(f"标题: {item['title']}\n")
                        f.write(f"URL: {item['url']}\n")

                        # 添加relateinfoid信息
                        if 'relateinfoid' in item and item['relateinfoid']:
                            f.write(f"RelateInfoID: {item['relateinfoid']}\n")

                        # 添加发包人信息
                        if 'sender_info' in item and item['sender_info']:
                            sender_info = item['sender_info']
                            f.write("\n【发包人信息】\n")
                            if sender_info.get('发包人名称'):
                                f.write(f"发包人名称: {sender_info['发包人名称']}\n")
                            if sender_info.get('发包人地址'):
                                f.write(f"发包人地址: {sender_info['发包人地址']}\n")
                            if sender_info.get('发包人电话'):
                                f.write(f"发包人电话: {sender_info['发包人电话']}\n")

                        f.write(f"\n内容:\n{item['content']}\n")

                        # 添加infoContent数据
                        if 'info_content' in item and item['info_content']:
                            f.write(f"\nInfoContent内容:\n{item['info_content']}\n")

                        # 添加JSON数据
                        if 'json_data' in item and item['json_data']:
                            f.write(f"\nAPI数据:\n{json.dumps(item['json_data'], ensure_ascii=False, indent=2)}\n")

                        # 添加表格数据
                        if item['tables']:
                            for table_idx, table in enumerate(item['tables']):
                                f.write(f"\n表格 {table_idx + 1}:\n")
                                for row in table:
                                    f.write(" | ".join(row) + "\n")

                        f.write("\n" + "=" * 80 + "\n\n")

            elif file_ext == '.xlsx':
                try:
                    from openpyxl import Workbook
                    from openpyxl.styles import Font, Alignment, Border, Side
                    wb = Workbook()
                    data_sheet = wb.active
                    data_sheet.title = "数据导出"
                    header_font = Font(bold=True)
                    center_aligned = Alignment(horizontal='center', vertical='center', wrap_text=True)
                    thin_border = Border(
                        left=Side(style='thin'),
                        right=Side(style='thin'),
                        top=Side(style='thin'),
                        bottom=Side(style='thin')
                    )
                    export_columns = [
                        "序号", "URL", "项目名称", "项目所在地", "发包人名称", "发包人地址", "发包人电话",
                        "承包人名称", "承包人地址", "承包人电话", "承包人项目经理（施工适用）", "技术负责人（项目总工）", "签约合同价（元）",
                        "公示期",  # 新增公示期列
                        "开标时间"  # 新增开标时间列
                    ]
                    # 动态插入开标时间额外信息字段
                    kaibiao_extra_keys = []
                    for item in export_data:
                        if '开标时间额外信息' in item and isinstance(item['开标时间额外信息'], dict):
                            for k in item['开标时间额外信息'].keys():
                                if k not in kaibiao_extra_keys:
                                    kaibiao_extra_keys.append(k)
                    # 插入到开标时间后
                    if kaibiao_extra_keys:
                        idx = export_columns.index("开标时间") + 1
                        for i, k in enumerate(kaibiao_extra_keys):
                            export_columns.insert(idx + i, k)
                    data_sheet.append(export_columns)
                    for cell in data_sheet[1]:
                        cell.font = header_font
                        cell.alignment = center_aligned
                        cell.border = thin_border
                    all_rows = []
                    for i, item in enumerate(export_data, 1):
                        url = item.get('url', '')
                        project_name = item.get('项目名称', item.get('project_name', item.get('title', '无标题')))
                        row_dict = {col: "" for col in export_columns}
                        row_dict["序号"] = i
                        row_dict["URL"] = url
                        row_dict["项目名称"] = project_name
                        contractor_fake_names = ["甲公司", "乙公司", "丙公司"]
                        contractor_name_value = ""
                        contractor_addr_value = ""
                        contractor_phone_value = ""
                        for col in export_columns:
                            if col in ["序号", "URL", "项目名称"]:
                                continue
                            value = ""
                            if col == "公示期":
                                value = item.get("公示期", "")
                            elif col == "开标时间":
                                value = item.get("开标时间", "")
                            elif '开标时间额外信息' in item and isinstance(item['开标时间额外信息'], dict) and col in item['开标时间额外信息']:
                                value = item['开标时间额外信息'][col]
                            elif col in item.get('sender_info', {}):
                                value = item['sender_info'][col]
                            if not value:
                                for table in item.get('tables', []):
                                    if not table or len(table) < 2:
                                        continue
                                    headers = table[0]
                                    for row in table[1:]:
                                        for idx, header in enumerate(headers):
                                            if header == col and idx < len(row):
                                                if row[idx]:
                                                    value = row[idx]
                                                    break
                                        if value:
                                            break
                                    if value:
                                        break
                            # 先暂存承包人三列的值
                            if col == "承包人名称":
                                contractor_name_value = value
                            elif col == "承包人地址":
                                contractor_addr_value = value
                            elif col == "承包人电话":
                                contractor_phone_value = value
                            row_dict[col] = value
                        # 过滤虚拟公司名并用table真实值覆盖
                        if contractor_name_value in contractor_fake_names:
                            found_real = False
                            for table in item.get('tables', []):
                                if not table or len(table) < 2:
                                    continue
                                headers = table[0]
                                try:
                                    idx_name = headers.index("承包人名称")
                                    idx_addr = headers.index("承包人地址") if "承包人地址" in headers else -1
                                    idx_phone = headers.index("承包人电话") if "承包人电话" in headers else -1
                                except ValueError:
                                    continue
                                for row in table[1:]:
                                    if idx_name < len(row):
                                        name = row[idx_name]
                                        if name not in contractor_fake_names and name:
                                            row_dict["承包人名称"] = name
                                            row_dict["承包人地址"] = row[idx_addr] if idx_addr >= 0 and idx_addr < len(row) else ""
                                            row_dict["承包人电话"] = row[idx_phone] if idx_phone >= 0 and idx_phone < len(row) else ""
                                            found_real = True
                                            break
                                if found_real:
                                    break
                            if not found_real:
                                row_dict["承包人名称"] = ""
                                row_dict["承包人地址"] = ""
                                row_dict["承包人电话"] = ""
                        row_data = [row_dict[col] for col in export_columns]
                        all_rows.append(row_data)
                    self.update_log(f"将导出 {len(all_rows)} 条数据记录")
                    row_counter = 2
                    for i, row_data in enumerate(all_rows, 1):
                        row_data[0] = i
                        data_sheet.append(row_data)
                        for cell in data_sheet[row_counter]:
                            cell.border = thin_border
                            cell.alignment = Alignment(vertical='center', wrap_text=True)
                        row_counter += 1
                    for col in data_sheet.columns:
                        max_length = 0
                        for cell in col:
                            try:
                                if cell.value and len(str(cell.value)) > max_length:
                                    max_length = len(str(cell.value))
                            except:
                                pass
                        adjusted_width = min(max_length + 2, 50)
                        if hasattr(col[0], 'column_letter') and col[0].column_letter:
                            data_sheet.column_dimensions[col[0].column_letter].width = adjusted_width
                    # 冻结第一列
                    data_sheet.freeze_panes = data_sheet['B2']
                    wb.save(file_path)
                    self.update_log(f"Excel数据已成功导出到: {file_path}")
                    return
                except ImportError:
                    self.update_log("导出Excel需要openpyxl库，请安装后重试")
                    return
                except Exception as e:
                    self.update_log(f"导出Excel时出错: {str(e)}")
                    return

            else:
                # 默认导出为JSON格式
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)  # 使用全部有效数据

            self.update_log(f"所有数据已成功导出到: {file_path}")
        except Exception as e:
            self.update_log(f"导出数据时出错: {str(e)}")

    def refresh_ui(self):
        """刷新UI到初始状态"""
        # 清空数据
        self.all_links = []
        self.all_data = []

        # 重置UI元素
        self.log_display.clear()
        self.data_display.clear()
        self.status_label.setText("就绪")
        self.progress_bar.setVisible(False)

        # 重置按钮状态
        self.start_button.setEnabled(True)
        self.fetch_button.setEnabled(False)

        # 重置日期选择
        self.days_combo.setCurrentIndex(0)  # 设置为第一个选项（近三天）

        # 记录日志
        self.log_display.append("已重置到初始状态")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    # 启动应用程序
    sys.exit(app.exec_())