# 网页资源抽取工具

这个工具用于抓取四川公共资源交易网站的数据，并提取`linkurl`信息。

## 功能
- 自动打开网页并点击"工程建设"和"签约履行"按钮
- 监听页面的所有网络请求
- 提取并打印响应中的`linkurl`信息
- 记录完整的请求参数和响应数据

## 安装依赖

```bash
# 使用国内镜像安装pip依赖
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple playwright

# 安装浏览器驱动
playwright install chromium
```

## 使用方法

```bash
python crawler.py
```

## 日志

程序运行过程中会生成`crawler.log`文件，同时也会在控制台输出日志信息。日志包含：
- 请求URL
- 请求参数
- 响应状态
- 响应内容
- 提取的linkurl

## 注意事项
- 程序默认以非无头模式运行浏览器，可以看到浏览器操作过程
- 如果"签约履行"按钮定位失败，程序会尝试其他定位方法
- 程序运行完成后会自动关闭浏览器 