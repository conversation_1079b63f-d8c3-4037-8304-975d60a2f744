import sys
import logging
import threading
import queue
import concurrent.futures
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QTextEdit, QSplitter, QLabel, QFileDialog, QHBoxLayout, QComboBox, QFormLayout, QDateEdit, QGridLayout, QFrame, QProgressBar
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QDate
from PyQt5.QtGui import QPixmap, QImage
import os
import time
import json
import io
import requests
from datetime import datetime, timedelta, date
from bs4 import BeautifulSoup
import re
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side
from openpyxl.utils import get_column_letter
import csv

# 导出字段
EXPORT_COLUMNS = [
    "项目名称", "项目所在地", "发包人名称", "发包人地址", "发包人电话",
    "承包人名称", "承包人地址", "承包人电话", "签约合同价", "签约合同价（其他价格形式）",
    "签订合同日期（施工、监理适用）", "承包人项目经理（施工适用）"
]

class YibinApiThread(QThread):
    log_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(list)

    def __init__(self, start_date, end_date, page_size=10):
        super().__init__()
        self.start_date = start_date
        self.end_date = end_date
        self.page_size = page_size

    def run(self):
        try:
            url = "https://ggzy.yibin.gov.cn/ggfwptwebapi/Web/service"
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0'
            }
            page_index = 1
            all_data = []
            total = 1
            while (page_index - 1) * self.page_size < total:
                payload = {
                    "action": "pageTongYong_SouSuo",
                    "pageIndex": page_index,
                    "pageSize": self.page_size,
                    "xiangMu_LeiXing": None,
                    "xinXi_LeiXing": "112",
                    "title": "",
                    "publish_StartTime": self.start_date,
                    "publish_EntTime": self.end_date
                }
                resp = requests.post(url, headers=headers, json=payload, timeout=20)
                if resp.status_code != 200:
                    self.log_signal.emit(f"请求失败: {resp.status_code}")
                    break
                data = resp.json()
                if data.get("code") != 200:
                    self.log_signal.emit(f"接口返回异常: {data.get('message')}")
                    break
                total = data.get("total", 0)
                items = data.get("data", [])
                self.log_signal.emit(f"第{page_index}页，获取{len(items)}条，总数{total}")
                all_data.extend(items)
                if not items:
                    break
                page_index += 1
            self.finished_signal.emit(all_data)
        except Exception as e:
            self.log_signal.emit(f"API线程异常: {str(e)}")
            self.finished_signal.emit([])

class YibinDetailThread(QThread):
    log_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int, int)  # 当前进度，总数
    finished_signal = pyqtSignal(list)

    def __init__(self, items, max_workers=10):
        super().__init__()
        self.items = items
        self.max_workers = max_workers  # 最大线程数

    def fetch_detail(self, item, idx):
        import time
        import re
        guid = item.get("guid")
        xiangmu_name = item.get("xiangMu_Name", "未知项目")
        
        # 使用正确的接口参数
        url = f"https://ggzy.yibin.gov.cn/ggfwptwebapi/Web/service"
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0'
        }
        
        # 使用正确的接口名称和参数
        try:
            payload = {
                "action": "getGCJS_HeTong_GongGao",
                "guid": guid
            }
            self.log_signal.emit(f"[{idx}] {xiangmu_name} - 使用接口: getGCJS_HeTong_GongGao, guid: {guid}")
            
            resp = requests.post(url, headers=headers, json=payload, timeout=15)
            self.log_signal.emit(f"[{idx}] 接口响应: 状态码={resp.status_code}")
            
            if resp.status_code == 200:
                result = resp.json()
                self.log_signal.emit(f"[{idx}] 接口返回: code={result.get('code')}, message={result.get('message', '')}")
                
                if result.get("code") == 200 and result.get("data"):
                    # 正确的数据在 data.heTong_GongGao 中
                    detail_data = result.get("data", {}).get("heTong_GongGao", {})
                    if detail_data:
                        self.log_signal.emit(f"[{idx}] 接口返回数据: {str(detail_data)[:200]}...")
                        
                        # 记录关键字段是否存在及其值
                        for key in ["ZhaoBiaoRen", "ZhongBiaoRen", "HeTongJia", "XiangMu_Name", "Area", 
                                   "ZhaoBiaoRen_DiZhi", "ZhaoBiaoRen_LianXi_DianHua", "ZhongBiaoRen_DiZhi", 
                                   "ZhongBiaoRen_LianXi_DianHua", "QiTa_JiaGe_XingShi_HeTongJia", 
                                   "QianDing_Time", "XiangMu_JingLi", "XiangMu_MiaoShu"]:
                            value = detail_data.get(key, "未找到")
                            self.log_signal.emit(f"[{idx}] 字段 {key} = {value}")
                        
                        # 从项目描述中提取建设地点
                        project_location = detail_data.get("Area", "")
                        xiangmu_miaoshu = detail_data.get("XiangMu_MiaoShu", "")
                        
                        if xiangmu_miaoshu:
                            # 尝试提取建设地点
                            location_patterns = [
                                r"建设地点[:：]\s*([^。\n]+)",  # 匹配"建设地点："后面的内容，直到句号或换行
                                r"工程地点[:：]\s*([^。\n]+)",   # 匹配"工程地点："后面的内容
                                r"项目位于\s*([^。\n]+)",       # 匹配"项目位于"后面的内容
                                r"位于\s*([^。\n]+)",           # 匹配"位于"后面的内容
                                r"地点[:：]\s*([^。\n]+)"       # 匹配"地点："后面的内容
                            ]
                            
                            extracted_location = ""
                            for pattern in location_patterns:
                                match = re.search(pattern, xiangmu_miaoshu)
                                if match:
                                    extracted_location = match.group(1).strip()
                                    self.log_signal.emit(f"[{idx}] 从项目描述中提取到地点: {extracted_location}")
                                    break
                            
                            if extracted_location:
                                project_location = extracted_location
                        
                        # 构建结果字典
                        result_dict = {
                            "项目名称": detail_data.get("XiangMu_Name", xiangmu_name),
                            "项目所在地": project_location,
                            "发包人名称": detail_data.get("ZhaoBiaoRen", ""),
                            "发包人地址": detail_data.get("ZhaoBiaoRen_DiZhi", ""),
                            "发包人电话": detail_data.get("ZhaoBiaoRen_LianXi_DianHua", ""),
                            "承包人名称": detail_data.get("ZhongBiaoRen", ""),
                            "承包人地址": detail_data.get("ZhongBiaoRen_DiZhi", ""),
                            "承包人电话": detail_data.get("ZhongBiaoRen_LianXi_DianHua", ""),
                            "签约合同价": detail_data.get("HeTongJia", ""),
                            "签约合同价（其他价格形式）": detail_data.get("QiTa_JiaGe_XingShi_HeTongJia", ""),
                            "签订合同日期（施工、监理适用）": detail_data.get("QianDing_Time", ""),
                            "承包人项目经理（施工适用）": detail_data.get("XiangMu_JingLi", "")
                        }
                        
                        # 记录最终结果中的值
                        self.log_signal.emit(f"[{idx}] 最终结果字典:")
                        for k, v in result_dict.items():
                            self.log_signal.emit(f"[{idx}] {k} = {v}")
                            
                        return idx, result_dict
                    else:
                        self.log_signal.emit(f"[{idx}] 接口返回数据中无heTong_GongGao字段")
                else:
                    self.log_signal.emit(f"[{idx}] 接口返回错误或无数据")
        except Exception as e:
            self.log_signal.emit(f"[{idx}] 接口请求异常: {str(e)}")
        
        # 如果接口调用失败，尝试解析HTML
        try:
            # 尝试几种可能的详情页URL格式
            for html_url_format in [
                f"https://ggzy.yibin.gov.cn/TPFront/InfoDetail/?infoid={guid}&categoryNum=112",
                f"https://ggzy.yibin.gov.cn/TPFront/infodetail.html?infoid={guid}&categoryNum=112"
            ]:
                self.log_signal.emit(f"[{idx}] {xiangmu_name} - 尝试HTML页面: {html_url_format}")
                resp = requests.get(html_url_format, headers=headers, timeout=15)
                
                if resp.status_code == 200:
                    self.log_signal.emit(f"[{idx}] HTML页面获取成功，内容长度: {len(resp.content)}")
                    soup = BeautifulSoup(resp.content, 'html.parser')
                    
                    # 查找所有表格
                    tables = soup.find_all('table')
                    self.log_signal.emit(f"[{idx}] 找到 {len(tables)} 个表格")
                    
                    info = {col: '' for col in EXPORT_COLUMNS}
                    for table_idx, table in enumerate(tables):
                        self.log_signal.emit(f"[{idx}] 解析表格 {table_idx+1}")
                        for row in table.find_all('tr'):
                            cells = row.find_all(['td', 'th'])
                            if len(cells) >= 2:
                                key = cells[0].get_text(strip=True)
                                value = cells[1].get_text(strip=True)
                                self.log_signal.emit(f"[{idx}] 表格单元格: {key} = {value}")
                                
                                for col in EXPORT_COLUMNS:
                                    if col in key and not info[col]:
                                        info[col] = value
                                        self.log_signal.emit(f"[{idx}] 匹配到字段: {col} = {value}")
                    
                    # 项目名称优先用item自带
                    if not info["项目名称"]:
                        info["项目名称"] = xiangmu_name
                        self.log_signal.emit(f"[{idx}] 使用列表项目名称: {xiangmu_name}")
                    
                    # 检查是否有有效数据
                    valid_fields = sum(1 for v in info.values() if v)
                    self.log_signal.emit(f"[{idx}] 有效字段数量: {valid_fields}/{len(info)}")
                    
                    if valid_fields > 1:  # 至少有项目名称和其他一个字段
                        return idx, info
        except Exception as e:
            self.log_signal.emit(f"[{idx}] HTML解析异常: {str(e)}")
        
        # 兜底：只返回项目名
        self.log_signal.emit(f"[{idx}] 所有尝试失败，只返回项目名称")
        return idx, {col: xiangmu_name if col == "项目名称" else '' for col in EXPORT_COLUMNS}

    def run(self):
        try:
            self.log_signal.emit(f"开始多线程处理{len(self.items)}个项目详情...")
            
            # 创建线程池
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有任务
                future_to_idx = {
                    executor.submit(self.fetch_detail, item, idx): idx 
                    for idx, item in enumerate(self.items)
                }
                
                # 保存结果，确保顺序
                results = [None] * len(self.items)
                
                # 处理完成的任务
                completed = 0
                for future in concurrent.futures.as_completed(future_to_idx):
                    idx = future_to_idx[future]
                    try:
                        result_idx, detail = future.result()
                        results[result_idx] = detail
                        
                        # 更新进度
                        completed += 1
                        self.progress_signal.emit(completed, len(self.items))
                        
                        # 每5个项目或最后一个时输出日志
                        if completed % 5 == 0 or completed == len(self.items):
                            self.log_signal.emit(f"已完成{completed}/{len(self.items)}个项目详情")
                    except Exception as e:
                        self.log_signal.emit(f"处理项目[{idx}]异常: {str(e)}")
                        results[idx] = {col: self.items[idx].get("xiangMu_Name", "") if col == "项目名称" else '' for col in EXPORT_COLUMNS}
            
            # 移除None值（如有）
            results = [r for r in results if r is not None]
            self.log_signal.emit(f"详情处理完成，共{len(results)}条有效数据")
            self.finished_signal.emit(results)
            
        except Exception as e:
            self.log_signal.emit(f"详情线程异常: {str(e)}")
            self.finished_signal.emit([])

class YibinMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("宜宾公共资源交易网数据抽取工具")
        self.setGeometry(100, 100, 900, 600)
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        form_layout = QHBoxLayout()
        form_layout.addWidget(QLabel("开始日期:"))
        self.start_date = QDateEdit(self)
        self.start_date.setCalendarPopup(True)
        self.start_date.setDate(QDate.currentDate().addDays(-7))
        form_layout.addWidget(self.start_date)
        form_layout.addWidget(QLabel("结束日期:"))
        self.end_date = QDateEdit(self)
        self.end_date.setCalendarPopup(True)
        self.end_date.setDate(QDate.currentDate())
        form_layout.addWidget(self.end_date)
        layout.addLayout(form_layout)
        btn_layout = QHBoxLayout()
        self.fetch_btn = QPushButton("获取数据")
        self.fetch_btn.clicked.connect(self.fetch_data)
        btn_layout.addWidget(self.fetch_btn)
        self.export_btn = QPushButton("导出表格")
        self.export_btn.clicked.connect(self.export_data)
        self.export_btn.setEnabled(False)
        btn_layout.addWidget(self.export_btn)
        layout.addLayout(btn_layout)
        
        # 添加进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        layout.addWidget(self.log_display)
        self.data = []
        self.details = []

    def fetch_data(self):
        self.log_display.clear()
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.fetch_btn.setEnabled(False)
        self.export_btn.setEnabled(False)
        
        start = self.start_date.date().toString("yyyy-MM-dd 00:00:00")
        end = self.end_date.date().toString("yyyy-MM-dd 23:59:59")
        self.api_thread = YibinApiThread(start, end)
        self.api_thread.log_signal.connect(self.log_display.append)
        self.api_thread.finished_signal.connect(self.on_api_finished)
        self.api_thread.start()

    def on_api_finished(self, items):
        self.data = items
        self.log_display.append(f"API获取完成，共{len(items)}条")
        if items:
            self.detail_thread = YibinDetailThread(items)
            self.detail_thread.log_signal.connect(self.log_display.append)
            self.detail_thread.progress_signal.connect(self.update_progress)
            self.detail_thread.finished_signal.connect(self.on_detail_finished)
            self.detail_thread.start()
        else:
            self.export_btn.setEnabled(False)
            self.fetch_btn.setEnabled(True)
            self.progress_bar.setVisible(False)

    def update_progress(self, current, total):
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)

    def on_detail_finished(self, details):
        self.details = details
        self.log_display.append(f"详情处理完成，共{len(details)}条")
        self.fetch_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        if details:
            self.export_btn.setEnabled(True)

    def export_data(self):
        if not self.details:
            self.log_display.append("无可导出数据")
            return
            
        # 添加时间戳到文件名
        timestamp = datetime.now().strftime("_%Y%m%d_%H%M%S")
        default_filename = f"宜宾数据导出{timestamp}.xlsx"
        
        file_path, _ = QFileDialog.getSaveFileName(self, "导出Excel", default_filename, "Excel文件 (*.xlsx)")
        if not file_path:
            return
            
        wb = Workbook()
        ws = wb.active
        ws.title = "宜宾数据"
        
        # 添加固定标题行 - 第一行
        title_text = "宜宾市公共资源交易中心数据"
        ws.append([title_text])
        ws.merge_cells(f'A1:{get_column_letter(len(EXPORT_COLUMNS))}1')
        title_cell = ws.cell(row=1, column=1)
        title_cell.font = Font(bold=True, size=14)
        title_cell.alignment = Alignment(horizontal='center')
        
        # 添加固定副标题行 - 第二行
        subtitle_text = f"导出时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        ws.append([subtitle_text])
        ws.merge_cells(f'A2:{get_column_letter(len(EXPORT_COLUMNS))}2')
        subtitle_cell = ws.cell(row=2, column=1)
        subtitle_cell.font = Font(size=11)
        subtitle_cell.alignment = Alignment(horizontal='center')
        
        # 空行
        ws.append([])
        
        # 添加字段标题行 - 第四行
        header_row = ws.row_dimensions[4]
        header_row.height = 20
        ws.append(EXPORT_COLUMNS)
        for i, col in enumerate(EXPORT_COLUMNS, 1):
            cell = ws.cell(row=4, column=i)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            # 设置边框
            cell.border = Border(
                left=Side(style='thin'), 
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
        
        # 添加数据行
        for row_idx, row_data in enumerate(self.details, 5):
            ws.append([row_data.get(col, "") for col in EXPORT_COLUMNS])
            # 设置数据单元格格式
            for col_idx, col in enumerate(EXPORT_COLUMNS, 1):
                cell = ws.cell(row=row_idx, column=col_idx)
                # 所有单元格文字居中
                cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
                cell.border = Border(
                    left=Side(style='thin'), 
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
            
        # 调整列宽
        for i, col in enumerate(EXPORT_COLUMNS, 1):
            ws.column_dimensions[get_column_letter(i)].width = max(15, len(col) * 1.5)
        
        # 冻结表头
        ws.freeze_panes = 'A5'  # 冻结第1-4行
            
        wb.save(file_path)
        self.log_display.append(f"导出完成: {file_path}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = YibinMainWindow()
    window.show()
    sys.exit(app.exec_()) 